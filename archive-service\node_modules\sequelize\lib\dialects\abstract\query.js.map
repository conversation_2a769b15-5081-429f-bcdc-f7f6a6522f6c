{"version": 3, "sources": ["../../../src/dialects/abstract/query.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst SqlString = require('../../sql-string');\nconst QueryTypes = require('../../query-types');\nconst Dot = require('dottie');\nconst deprecations = require('../../utils/deprecations');\nconst uuid = require('uuid').v4;\nconst { safeStringifyJson } = require('../../utils.js');\n\nclass AbstractQuery {\n\n  constructor(connection, sequelize, options) {\n    this.uuid = uuid();\n    this.connection = connection;\n    this.instance = options.instance;\n    this.model = options.model;\n    this.sequelize = sequelize;\n    this.options = {\n      plain: false,\n      raw: false,\n      // eslint-disable-next-line no-console\n      logging: console.log,\n      ...options\n    };\n    this.checkLoggingOption();\n\n    if (options.rawErrors) {\n      // The default implementation in AbstractQuery just returns the same\n      // error object. By overidding this.formatError, this saves every dialect\n      // having to check for options.rawErrors in their own formatError\n      // implementations.\n      this.formatError = AbstractQuery.prototype.formatError;\n    }\n  }\n\n  /**\n   * rewrite query with parameters\n   *\n   * Examples:\n   *\n   *   query.formatBindParameters('select $1 as foo', ['fooval']);\n   *\n   *   query.formatBindParameters('select $foo as foo', { foo: 'fooval' });\n   *\n   * Options\n   *   skipUnescape: bool, skip unescaping $$\n   *   skipValueReplace: bool, do not replace (but do unescape $$). Check correct syntax and if all values are available\n   *\n   * @param {string} sql\n   * @param {object|Array} values\n   * @param {string} dialect\n   * @param {Function} [replacementFunc]\n   * @param {object} [options]\n   * @private\n   */\n  static formatBindParameters(sql, values, dialect, replacementFunc, options) {\n    if (!values) {\n      return [sql, []];\n    }\n\n    options = options || {};\n    if (typeof replacementFunc !== 'function') {\n      options = replacementFunc || {};\n      replacementFunc = undefined;\n    }\n\n    if (!replacementFunc) {\n      if (options.skipValueReplace) {\n        replacementFunc = (match, key, values) => {\n          if (values[key] !== undefined) {\n            return match;\n          }\n          return undefined;\n        };\n      } else {\n        replacementFunc = (match, key, values, timeZone, dialect) => {\n          if (values[key] !== undefined) {\n            return SqlString.escape(values[key], timeZone, dialect);\n          }\n          return undefined;\n        };\n      }\n    } else if (options.skipValueReplace) {\n      const origReplacementFunc = replacementFunc;\n      replacementFunc = (match, key, values, timeZone, dialect, options) => {\n        if (origReplacementFunc(match, key, values, timeZone, dialect, options) !== undefined) {\n          return match;\n        }\n        return undefined;\n      };\n    }\n\n    const timeZone = null;\n    const list = Array.isArray(values);\n    sql = sql.replace(/\\B\\$(\\$|\\w+)/g, (match, key) => {\n      if ('$' === key) {\n        return options.skipUnescape ? match : key;\n      }\n\n      let replVal;\n      if (list) {\n        if (key.match(/^[1-9]\\d*$/)) {\n          key = key - 1;\n          replVal = replacementFunc(match, key, values, timeZone, dialect, options);\n        }\n      } else if (!key.match(/^\\d*$/)) {\n        replVal = replacementFunc(match, key, values, timeZone, dialect, options);\n      }\n      if (replVal === undefined) {\n        throw new Error(`Named bind parameter \"${match}\" has no value in the given object.`);\n      }\n      return replVal;\n    });\n    return [sql, []];\n  }\n\n  /**\n   * Formats a raw database error from the database library into a common Sequelize exception.\n   *\n   * @param {Error} error The exception object.\n   * @param {object} errStack The stack trace that started the database query.\n   * @returns {BaseError} the new formatted error object.\n   */\n  formatError(error, errStack) {\n    // Default implementation, no formatting.\n    // Each dialect overrides this method to parse errors from their respective the database engines.\n    error.stack = errStack;\n\n    return error;\n  }\n\n  /**\n   * Execute the passed sql query.\n   *\n   * Examples:\n   *\n   *     query.run('SELECT 1')\n   *\n   * @private\n   */\n  run() {\n    throw new Error('The run method wasn\\'t overwritten!');\n  }\n\n  /**\n   * Check the logging option of the instance and print deprecation warnings.\n   *\n   * @private\n   */\n  checkLoggingOption() {\n    if (this.options.logging === true) {\n      deprecations.noTrueLogging();\n      // eslint-disable-next-line no-console\n      this.options.logging = console.log;\n    }\n  }\n\n  /**\n   * Get the attributes of an insert query, which contains the just inserted id.\n   *\n   * @returns {string} The field name.\n   * @private\n   */\n  getInsertIdField() {\n    return 'insertId';\n  }\n\n  getUniqueConstraintErrorMessage(field) {\n    let message = field ? `${field} must be unique` : 'Must be unique';\n\n    if (field && this.model) {\n      for (const key of Object.keys(this.model.uniqueKeys)) {\n        if (this.model.uniqueKeys[key].fields.includes(field.replace(/\"/g, ''))) {\n          if (this.model.uniqueKeys[key].msg) {\n            message = this.model.uniqueKeys[key].msg;\n          }\n        }\n      }\n    }\n    return message;\n  }\n\n  isRawQuery() {\n    return this.options.type === QueryTypes.RAW;\n  }\n\n  isVersionQuery() {\n    return this.options.type === QueryTypes.VERSION;\n  }\n\n  isUpsertQuery() {\n    return this.options.type === QueryTypes.UPSERT;\n  }\n\n  isInsertQuery(results, metaData) {\n    let result = true;\n\n    if (this.options.type === QueryTypes.INSERT) {\n      return true;\n    }\n\n    // is insert query if sql contains insert into\n    result = result && this.sql.toLowerCase().startsWith('insert into');\n\n    // is insert query if no results are passed or if the result has the inserted id\n    result = result && (!results || Object.prototype.hasOwnProperty.call(results, this.getInsertIdField()));\n\n    // is insert query if no metadata are passed or if the metadata has the inserted id\n    result = result && (!metaData || Object.prototype.hasOwnProperty.call(metaData, this.getInsertIdField()));\n\n    return result;\n  }\n\n  handleInsertQuery(results, metaData) {\n    if (this.instance) {\n      // add the inserted row id to the instance\n      const autoIncrementAttribute = this.model.autoIncrementAttribute;\n      let id = null;\n\n      id = id || results && results[this.getInsertIdField()];\n      id = id || metaData && metaData[this.getInsertIdField()];\n\n      this.instance[autoIncrementAttribute] = id;\n    }\n  }\n\n  isShowTablesQuery() {\n    return this.options.type === QueryTypes.SHOWTABLES;\n  }\n\n  handleShowTablesQuery(results) {\n    return _.flatten(results.map(resultSet => Object.values(resultSet)));\n  }\n\n  isShowIndexesQuery() {\n    return this.options.type === QueryTypes.SHOWINDEXES;\n  }\n\n  isShowConstraintsQuery() {\n    return this.options.type === QueryTypes.SHOWCONSTRAINTS;\n  }\n\n  isDescribeQuery() {\n    return this.options.type === QueryTypes.DESCRIBE;\n  }\n\n  isSelectQuery() {\n    return this.options.type === QueryTypes.SELECT;\n  }\n\n  isBulkUpdateQuery() {\n    return this.options.type === QueryTypes.BULKUPDATE;\n  }\n\n  isBulkDeleteQuery() {\n    return this.options.type === QueryTypes.BULKDELETE;\n  }\n\n  isForeignKeysQuery() {\n    return this.options.type === QueryTypes.FOREIGNKEYS;\n  }\n\n  isUpdateQuery() {\n    return this.options.type === QueryTypes.UPDATE;\n  }\n\n  handleSelectQuery(results) {\n    let result = null;\n\n    // Map raw fields to names if a mapping is provided\n    if (this.options.fieldMap) {\n      const fieldMap = this.options.fieldMap;\n      results = results.map(result => _.reduce(fieldMap, (result, name, field) => {\n        if (result[field] !== undefined && name !== field) {\n          result[name] = result[field];\n          delete result[field];\n        }\n        return result;\n      }, result));\n    }\n\n    // Raw queries\n    if (this.options.raw) {\n      result = results.map(result => {\n        let o = {};\n\n        for (const key in result) {\n          if (Object.prototype.hasOwnProperty.call(result, key)) {\n            o[key] = result[key];\n          }\n        }\n\n        if (this.options.nest) {\n          o = Dot.transform(o);\n        }\n\n        return o;\n      });\n    // Queries with include\n    } else if (this.options.hasJoin === true) {\n      results = AbstractQuery._groupJoinData(results, {\n        model: this.model,\n        includeMap: this.options.includeMap,\n        includeNames: this.options.includeNames\n      }, {\n        checkExisting: this.options.hasMultiAssociation\n      });\n\n      result = this.model.bulkBuild(results, {\n        isNewRecord: false,\n        include: this.options.include,\n        includeNames: this.options.includeNames,\n        includeMap: this.options.includeMap,\n        includeValidated: true,\n        attributes: this.options.originalAttributes || this.options.attributes,\n        raw: true\n      });\n    // Regular queries\n    } else {\n      result = this.model.bulkBuild(results, {\n        isNewRecord: false,\n        raw: true,\n        attributes: this.options.originalAttributes || this.options.attributes\n      });\n    }\n\n    // return the first real model instance if options.plain is set (e.g. Model.find)\n    if (this.options.plain) {\n      result = result.length === 0 ? null : result[0];\n    }\n    return result;\n  }\n\n  isShowOrDescribeQuery() {\n    let result = false;\n\n    result = result || this.sql.toLowerCase().startsWith('show');\n    result = result || this.sql.toLowerCase().startsWith('describe');\n\n    return result;\n  }\n\n  isCallQuery() {\n    return this.sql.toLowerCase().startsWith('call');\n  }\n\n  /**\n   * @param {string} sql\n   * @param {Function} debugContext\n   * @param {Array|object} parameters\n   * @protected\n   * @returns {Function} A function to call after the query was completed.\n   */\n  _logQuery(sql, debugContext, parameters) {\n    const { connection, options } = this;\n    const benchmark = this.sequelize.options.benchmark || options.benchmark;\n    const logQueryParameters = this.sequelize.options.logQueryParameters || options.logQueryParameters;\n    const startTime = Date.now();\n    let logParameter = '';\n\n    if (logQueryParameters && parameters) {\n      const delimiter = sql.endsWith(';') ? '' : ';';\n      let paramStr;\n      if (Array.isArray(parameters)) {\n        paramStr = parameters.map(p=>safeStringifyJson(p)).join(', ');\n      } else {\n        paramStr = safeStringifyJson(parameters);\n      }\n      logParameter = `${delimiter} ${paramStr}`;\n    }\n    const fmt = `(${connection.uuid || 'default'}): ${sql}${logParameter}`;\n    const msg = `Executing ${fmt}`;\n    debugContext(msg);\n    if (!benchmark) {\n      this.sequelize.log(`Executing ${fmt}`, options);\n    }\n    return () => {\n      const afterMsg = `Executed ${fmt}`;\n      debugContext(afterMsg);\n      if (benchmark) {\n        this.sequelize.log(afterMsg, Date.now() - startTime, options);\n      }\n    };\n  }\n\n  /**\n   * The function takes the result of the query execution and groups\n   * the associated data by the callee.\n   *\n   * Example:\n   *   groupJoinData([\n   *     {\n   *       some: 'data',\n   *       id: 1,\n   *       association: { foo: 'bar', id: 1 }\n   *     }, {\n   *       some: 'data',\n   *       id: 1,\n   *       association: { foo: 'bar', id: 2 }\n   *     }, {\n   *       some: 'data',\n   *       id: 1,\n   *       association: { foo: 'bar', id: 3 }\n   *     }\n   *   ])\n   *\n   * Result:\n   *   Something like this:\n   *\n   *   [\n   *     {\n   *       some: 'data',\n   *       id: 1,\n   *       association: [\n   *         { foo: 'bar', id: 1 },\n   *         { foo: 'bar', id: 2 },\n   *         { foo: 'bar', id: 3 }\n   *       ]\n   *     }\n   *   ]\n   *\n   * @param {Array} rows\n   * @param {object} includeOptions\n   * @param {object} options\n   * @private\n   */\n  static _groupJoinData(rows, includeOptions, options) {\n\n    /*\n     * Assumptions\n     * ID is not necessarily the first field\n     * All fields for a level is grouped in the same set (i.e. Panel.id, Task.id, Panel.title is not possible)\n     * Parent keys will be seen before any include/child keys\n     * Previous set won't necessarily be parent set (one parent could have two children, one child would then be previous set for the other)\n     */\n\n    /*\n     * Author (MH) comment: This code is an unreadable mess, but it's performant.\n     * groupJoinData is a performance critical function so we prioritize perf over readability.\n     */\n    if (!rows.length) {\n      return [];\n    }\n\n    // Generic looping\n    let i;\n    let length;\n    let $i;\n    let $length;\n    // Row specific looping\n    let rowsI;\n    let row;\n    const rowsLength = rows.length;\n    // Key specific looping\n    let keys;\n    let key;\n    let keyI;\n    let keyLength;\n    let prevKey;\n    let values;\n    let topValues;\n    let topExists;\n    const checkExisting = options.checkExisting;\n    // If we don't have to deduplicate we can pre-allocate the resulting array\n    let itemHash;\n    let parentHash;\n    let topHash;\n    const results = checkExisting ? [] : new Array(rowsLength);\n    const resultMap = {};\n    const includeMap = {};\n    // Result variables for the respective functions\n    let $keyPrefix;\n    let $keyPrefixString;\n    let $prevKeyPrefixString; // eslint-disable-line\n    let $prevKeyPrefix;\n    let $lastKeyPrefix;\n    let $current;\n    let $parent;\n    // Map each key to an include option\n    let previousPiece;\n    const buildIncludeMap = piece => {\n      if (Object.prototype.hasOwnProperty.call($current.includeMap, piece)) {\n        includeMap[key] = $current = $current.includeMap[piece];\n        if (previousPiece) {\n          previousPiece = `${previousPiece}.${piece}`;\n        } else {\n          previousPiece = piece;\n        }\n        includeMap[previousPiece] = $current;\n      }\n    };\n    // Calculate the string prefix of a key ('User.Results' for 'User.Results.id')\n    const keyPrefixStringMemo = {};\n    const keyPrefixString = (key, memo) => {\n      if (!Object.prototype.hasOwnProperty.call(memo, key)) {\n        memo[key] = key.substr(0, key.lastIndexOf('.'));\n      }\n      return memo[key];\n    };\n    // Removes the prefix from a key ('id' for 'User.Results.id')\n    const removeKeyPrefixMemo = {};\n    const removeKeyPrefix = key => {\n      if (!Object.prototype.hasOwnProperty.call(removeKeyPrefixMemo, key)) {\n        const index = key.lastIndexOf('.');\n        removeKeyPrefixMemo[key] = key.substr(index === -1 ? 0 : index + 1);\n      }\n      return removeKeyPrefixMemo[key];\n    };\n    // Calculates the array prefix of a key (['User', 'Results'] for 'User.Results.id')\n    const keyPrefixMemo = {};\n    const keyPrefix = key => {\n      // We use a double memo and keyPrefixString so that different keys with the same prefix will receive the same array instead of differnet arrays with equal values\n      if (!Object.prototype.hasOwnProperty.call(keyPrefixMemo, key)) {\n        const prefixString = keyPrefixString(key, keyPrefixStringMemo);\n        if (!Object.prototype.hasOwnProperty.call(keyPrefixMemo, prefixString)) {\n          keyPrefixMemo[prefixString] = prefixString ? prefixString.split('.') : [];\n        }\n        keyPrefixMemo[key] = keyPrefixMemo[prefixString];\n      }\n      return keyPrefixMemo[key];\n    };\n    // Calcuate the last item in the array prefix ('Results' for 'User.Results.id')\n    const lastKeyPrefixMemo = {};\n    const lastKeyPrefix = key => {\n      if (!Object.prototype.hasOwnProperty.call(lastKeyPrefixMemo, key)) {\n        const prefix = keyPrefix(key);\n        const length = prefix.length;\n\n        lastKeyPrefixMemo[key] = !length ? '' : prefix[length - 1];\n      }\n      return lastKeyPrefixMemo[key];\n    };\n    const getUniqueKeyAttributes = model => {\n      let uniqueKeyAttributes = _.chain(model.uniqueKeys);\n      uniqueKeyAttributes = uniqueKeyAttributes\n        .result(`${uniqueKeyAttributes.findKey()}.fields`)\n        .map(field => _.findKey(model.attributes, chr => chr.field === field))\n        .value();\n\n      return uniqueKeyAttributes;\n    };\n    const stringify = obj => obj instanceof Buffer ? obj.toString('hex') : obj;\n    let primaryKeyAttributes;\n    let uniqueKeyAttributes;\n    let prefix;\n\n    for (rowsI = 0; rowsI < rowsLength; rowsI++) {\n      row = rows[rowsI];\n\n      // Keys are the same for all rows, so only need to compute them on the first row\n      if (rowsI === 0) {\n        keys = _.sortBy(Object.keys(row), item => [item.split('.').length]);\n        keyLength = keys.length;\n      }\n\n      if (checkExisting) {\n        topExists = false;\n\n        // Compute top level hash key (this is usually just the primary key values)\n        $length = includeOptions.model.primaryKeyAttributes.length;\n        topHash = '';\n        if ($length === 1) {\n          topHash = stringify(row[includeOptions.model.primaryKeyAttributes[0]]);\n        }\n        else if ($length > 1) {\n          for ($i = 0; $i < $length; $i++) {\n            topHash += stringify(row[includeOptions.model.primaryKeyAttributes[$i]]);\n          }\n        }\n        else if (!_.isEmpty(includeOptions.model.uniqueKeys)) {\n          uniqueKeyAttributes = getUniqueKeyAttributes(includeOptions.model);\n          for ($i = 0; $i < uniqueKeyAttributes.length; $i++) {\n            topHash += row[uniqueKeyAttributes[$i]];\n          }\n        }\n      }\n\n      topValues = values = {};\n      $prevKeyPrefix = undefined;\n      for (keyI = 0; keyI < keyLength; keyI++) {\n        key = keys[keyI];\n\n        // The string prefix isn't actualy needed\n        // We use it so keyPrefix for different keys will resolve to the same array if they have the same prefix\n        // TODO: Find a better way?\n        $keyPrefixString = keyPrefixString(key, keyPrefixStringMemo);\n        $keyPrefix = keyPrefix(key);\n\n        // On the first row we compute the includeMap\n        if (rowsI === 0 && !Object.prototype.hasOwnProperty.call(includeMap, key)) {\n          if (!$keyPrefix.length) {\n            includeMap[key] = includeMap[''] = includeOptions;\n          } else {\n            $current = includeOptions;\n            previousPiece = undefined;\n            $keyPrefix.forEach(buildIncludeMap);\n          }\n        }\n        // End of key set\n        if ($prevKeyPrefix !== undefined && $prevKeyPrefix !== $keyPrefix) {\n          if (checkExisting) {\n            // Compute hash key for this set instance\n            // TODO: Optimize\n            length = $prevKeyPrefix.length;\n            $parent = null;\n            parentHash = null;\n\n            if (length) {\n              for (i = 0; i < length; i++) {\n                prefix = $parent ? `${$parent}.${$prevKeyPrefix[i]}` : $prevKeyPrefix[i];\n                primaryKeyAttributes = includeMap[prefix].model.primaryKeyAttributes;\n                $length = primaryKeyAttributes.length;\n                itemHash = prefix;\n                if ($length === 1) {\n                  itemHash += stringify(row[`${prefix}.${primaryKeyAttributes[0]}`]);\n                }\n                else if ($length > 1) {\n                  for ($i = 0; $i < $length; $i++) {\n                    itemHash += stringify(row[`${prefix}.${primaryKeyAttributes[$i]}`]);\n                  }\n                }\n                else if (!_.isEmpty(includeMap[prefix].model.uniqueKeys)) {\n                  uniqueKeyAttributes = getUniqueKeyAttributes(includeMap[prefix].model);\n                  for ($i = 0; $i < uniqueKeyAttributes.length; $i++) {\n                    itemHash += row[`${prefix}.${uniqueKeyAttributes[$i]}`];\n                  }\n                }\n                if (!parentHash) {\n                  parentHash = topHash;\n                }\n\n                itemHash = parentHash + itemHash;\n                $parent = prefix;\n                if (i < length - 1) {\n                  parentHash = itemHash;\n                }\n              }\n            } else {\n              itemHash = topHash;\n            }\n\n            if (itemHash === topHash) {\n              if (!resultMap[itemHash]) {\n                resultMap[itemHash] = values;\n              } else {\n                topExists = true;\n              }\n            } else if (!resultMap[itemHash]) {\n              $parent = resultMap[parentHash];\n              $lastKeyPrefix = lastKeyPrefix(prevKey);\n\n              if (includeMap[prevKey].association.isSingleAssociation) {\n                if ($parent) {\n                  $parent[$lastKeyPrefix] = resultMap[itemHash] = values;\n                }\n              } else {\n                if (!$parent[$lastKeyPrefix]) {\n                  $parent[$lastKeyPrefix] = [];\n                }\n                $parent[$lastKeyPrefix].push(resultMap[itemHash] = values);\n              }\n            }\n\n            // Reset values\n            values = {};\n          } else {\n            // If checkExisting is false it's because there's only 1:1 associations in this query\n            // However we still need to map onto the appropriate parent\n            // For 1:1 we map forward, initializing the value object on the parent to be filled in the next iterations of the loop\n            $current = topValues;\n            length = $keyPrefix.length;\n            if (length) {\n              for (i = 0; i < length; i++) {\n                if (i === length - 1) {\n                  values = $current[$keyPrefix[i]] = {};\n                }\n                $current = $current[$keyPrefix[i]] || {};\n              }\n            }\n          }\n        }\n\n        // End of iteration, set value and set prev values (for next iteration)\n        values[removeKeyPrefix(key)] = row[key];\n        prevKey = key;\n        $prevKeyPrefix = $keyPrefix;\n        $prevKeyPrefixString = $keyPrefixString;\n      }\n\n      if (checkExisting) {\n        length = $prevKeyPrefix.length;\n        $parent = null;\n        parentHash = null;\n\n        if (length) {\n          for (i = 0; i < length; i++) {\n            prefix = $parent ? `${$parent}.${$prevKeyPrefix[i]}` : $prevKeyPrefix[i];\n            primaryKeyAttributes = includeMap[prefix].model.primaryKeyAttributes;\n            $length = primaryKeyAttributes.length;\n            itemHash = prefix;\n            if ($length === 1) {\n              itemHash += stringify(row[`${prefix}.${primaryKeyAttributes[0]}`]);\n            }\n            else if ($length > 0) {\n              for ($i = 0; $i < $length; $i++) {\n                itemHash += stringify(row[`${prefix}.${primaryKeyAttributes[$i]}`]);\n              }\n            }\n            else if (!_.isEmpty(includeMap[prefix].model.uniqueKeys)) {\n              uniqueKeyAttributes = getUniqueKeyAttributes(includeMap[prefix].model);\n              for ($i = 0; $i < uniqueKeyAttributes.length; $i++) {\n                itemHash += row[`${prefix}.${uniqueKeyAttributes[$i]}`];\n              }\n            }\n            if (!parentHash) {\n              parentHash = topHash;\n            }\n\n            itemHash = parentHash + itemHash;\n            $parent = prefix;\n            if (i < length - 1) {\n              parentHash = itemHash;\n            }\n          }\n        } else {\n          itemHash = topHash;\n        }\n\n        if (itemHash === topHash) {\n          if (!resultMap[itemHash]) {\n            resultMap[itemHash] = values;\n          } else {\n            topExists = true;\n          }\n        } else if (!resultMap[itemHash]) {\n          $parent = resultMap[parentHash];\n          $lastKeyPrefix = lastKeyPrefix(prevKey);\n\n          if (includeMap[prevKey].association.isSingleAssociation) {\n            if ($parent) {\n              $parent[$lastKeyPrefix] = resultMap[itemHash] = values;\n            }\n          } else {\n            if (!$parent[$lastKeyPrefix]) {\n              $parent[$lastKeyPrefix] = [];\n            }\n            $parent[$lastKeyPrefix].push(resultMap[itemHash] = values);\n          }\n        }\n        if (!topExists) {\n          results.push(topValues);\n        }\n      } else {\n        results[rowsI] = topValues;\n      }\n    }\n\n    return results;\n  }\n}\n\nmodule.exports = AbstractQuery;\nmodule.exports.AbstractQuery = AbstractQuery;\nmodule.exports.default = AbstractQuery;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,YAAY,QAAQ;AAC1B,MAAM,aAAa,QAAQ;AAC3B,MAAM,MAAM,QAAQ;AACpB,MAAM,eAAe,QAAQ;AAC7B,MAAM,OAAO,QAAQ,QAAQ;AAC7B,MAAM,EAAE,sBAAsB,QAAQ;AAEtC,oBAAoB;AAAA,EAElB,YAAY,YAAY,WAAW,SAAS;AAC1C,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,WAAW,QAAQ;AACxB,SAAK,QAAQ,QAAQ;AACrB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,MACb,OAAO;AAAA,MACP,KAAK;AAAA,MAEL,SAAS,QAAQ;AAAA,OACd;AAEL,SAAK;AAEL,QAAI,QAAQ,WAAW;AAKrB,WAAK,cAAc,cAAc,UAAU;AAAA;AAAA;AAAA,SAwBxC,qBAAqB,KAAK,QAAQ,SAAS,iBAAiB,SAAS;AAC1E,QAAI,CAAC,QAAQ;AACX,aAAO,CAAC,KAAK;AAAA;AAGf,cAAU,WAAW;AACrB,QAAI,OAAO,oBAAoB,YAAY;AACzC,gBAAU,mBAAmB;AAC7B,wBAAkB;AAAA;AAGpB,QAAI,CAAC,iBAAiB;AACpB,UAAI,QAAQ,kBAAkB;AAC5B,0BAAkB,CAAC,OAAO,KAAK,YAAW;AACxC,cAAI,QAAO,SAAS,QAAW;AAC7B,mBAAO;AAAA;AAET,iBAAO;AAAA;AAAA,aAEJ;AACL,0BAAkB,CAAC,OAAO,KAAK,SAAQ,WAAU,aAAY;AAC3D,cAAI,QAAO,SAAS,QAAW;AAC7B,mBAAO,UAAU,OAAO,QAAO,MAAM,WAAU;AAAA;AAEjD,iBAAO;AAAA;AAAA;AAAA,eAGF,QAAQ,kBAAkB;AACnC,YAAM,sBAAsB;AAC5B,wBAAkB,CAAC,OAAO,KAAK,SAAQ,WAAU,UAAS,aAAY;AACpE,YAAI,oBAAoB,OAAO,KAAK,SAAQ,WAAU,UAAS,cAAa,QAAW;AACrF,iBAAO;AAAA;AAET,eAAO;AAAA;AAAA;AAIX,UAAM,WAAW;AACjB,UAAM,OAAO,MAAM,QAAQ;AAC3B,UAAM,IAAI,QAAQ,iBAAiB,CAAC,OAAO,QAAQ;AACjD,UAAI,AAAQ,QAAR,KAAa;AACf,eAAO,QAAQ,eAAe,QAAQ;AAAA;AAGxC,UAAI;AACJ,UAAI,MAAM;AACR,YAAI,IAAI,MAAM,eAAe;AAC3B,gBAAM,MAAM;AACZ,oBAAU,gBAAgB,OAAO,KAAK,QAAQ,UAAU,SAAS;AAAA;AAAA,iBAE1D,CAAC,IAAI,MAAM,UAAU;AAC9B,kBAAU,gBAAgB,OAAO,KAAK,QAAQ,UAAU,SAAS;AAAA;AAEnE,UAAI,YAAY,QAAW;AACzB,cAAM,IAAI,MAAM,yBAAyB;AAAA;AAE3C,aAAO;AAAA;AAET,WAAO,CAAC,KAAK;AAAA;AAAA,EAUf,YAAY,OAAO,UAAU;AAG3B,UAAM,QAAQ;AAEd,WAAO;AAAA;AAAA,EAYT,MAAM;AACJ,UAAM,IAAI,MAAM;AAAA;AAAA,EAQlB,qBAAqB;AACnB,QAAI,KAAK,QAAQ,YAAY,MAAM;AACjC,mBAAa;AAEb,WAAK,QAAQ,UAAU,QAAQ;AAAA;AAAA;AAAA,EAUnC,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,gCAAgC,OAAO;AACrC,QAAI,UAAU,QAAQ,GAAG,yBAAyB;AAElD,QAAI,SAAS,KAAK,OAAO;AACvB,iBAAW,OAAO,OAAO,KAAK,KAAK,MAAM,aAAa;AACpD,YAAI,KAAK,MAAM,WAAW,KAAK,OAAO,SAAS,MAAM,QAAQ,MAAM,MAAM;AACvE,cAAI,KAAK,MAAM,WAAW,KAAK,KAAK;AAClC,sBAAU,KAAK,MAAM,WAAW,KAAK;AAAA;AAAA;AAAA;AAAA;AAK7C,WAAO;AAAA;AAAA,EAGT,aAAa;AACX,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,iBAAiB;AACf,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,gBAAgB;AACd,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,cAAc,SAAS,UAAU;AAC/B,QAAI,SAAS;AAEb,QAAI,KAAK,QAAQ,SAAS,WAAW,QAAQ;AAC3C,aAAO;AAAA;AAIT,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AAGrD,aAAS,UAAW,EAAC,WAAW,OAAO,UAAU,eAAe,KAAK,SAAS,KAAK;AAGnF,aAAS,UAAW,EAAC,YAAY,OAAO,UAAU,eAAe,KAAK,UAAU,KAAK;AAErF,WAAO;AAAA;AAAA,EAGT,kBAAkB,SAAS,UAAU;AACnC,QAAI,KAAK,UAAU;AAEjB,YAAM,yBAAyB,KAAK,MAAM;AAC1C,UAAI,KAAK;AAET,WAAK,MAAM,WAAW,QAAQ,KAAK;AACnC,WAAK,MAAM,YAAY,SAAS,KAAK;AAErC,WAAK,SAAS,0BAA0B;AAAA;AAAA;AAAA,EAI5C,oBAAoB;AAClB,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,sBAAsB,SAAS;AAC7B,WAAO,EAAE,QAAQ,QAAQ,IAAI,eAAa,OAAO,OAAO;AAAA;AAAA,EAG1D,qBAAqB;AACnB,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,yBAAyB;AACvB,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,kBAAkB;AAChB,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,gBAAgB;AACd,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,oBAAoB;AAClB,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,oBAAoB;AAClB,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,qBAAqB;AACnB,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,gBAAgB;AACd,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA;AAAA,EAG1C,kBAAkB,SAAS;AACzB,QAAI,SAAS;AAGb,QAAI,KAAK,QAAQ,UAAU;AACzB,YAAM,WAAW,KAAK,QAAQ;AAC9B,gBAAU,QAAQ,IAAI,aAAU,EAAE,OAAO,UAAU,CAAC,SAAQ,MAAM,UAAU;AAC1E,YAAI,QAAO,WAAW,UAAa,SAAS,OAAO;AACjD,kBAAO,QAAQ,QAAO;AACtB,iBAAO,QAAO;AAAA;AAEhB,eAAO;AAAA,SACN;AAAA;AAIL,QAAI,KAAK,QAAQ,KAAK;AACpB,eAAS,QAAQ,IAAI,aAAU;AAC7B,YAAI,IAAI;AAER,mBAAW,OAAO,SAAQ;AACxB,cAAI,OAAO,UAAU,eAAe,KAAK,SAAQ,MAAM;AACrD,cAAE,OAAO,QAAO;AAAA;AAAA;AAIpB,YAAI,KAAK,QAAQ,MAAM;AACrB,cAAI,IAAI,UAAU;AAAA;AAGpB,eAAO;AAAA;AAAA,eAGA,KAAK,QAAQ,YAAY,MAAM;AACxC,gBAAU,cAAc,eAAe,SAAS;AAAA,QAC9C,OAAO,KAAK;AAAA,QACZ,YAAY,KAAK,QAAQ;AAAA,QACzB,cAAc,KAAK,QAAQ;AAAA,SAC1B;AAAA,QACD,eAAe,KAAK,QAAQ;AAAA;AAG9B,eAAS,KAAK,MAAM,UAAU,SAAS;AAAA,QACrC,aAAa;AAAA,QACb,SAAS,KAAK,QAAQ;AAAA,QACtB,cAAc,KAAK,QAAQ;AAAA,QAC3B,YAAY,KAAK,QAAQ;AAAA,QACzB,kBAAkB;AAAA,QAClB,YAAY,KAAK,QAAQ,sBAAsB,KAAK,QAAQ;AAAA,QAC5D,KAAK;AAAA;AAAA,WAGF;AACL,eAAS,KAAK,MAAM,UAAU,SAAS;AAAA,QACrC,aAAa;AAAA,QACb,KAAK;AAAA,QACL,YAAY,KAAK,QAAQ,sBAAsB,KAAK,QAAQ;AAAA;AAAA;AAKhE,QAAI,KAAK,QAAQ,OAAO;AACtB,eAAS,OAAO,WAAW,IAAI,OAAO,OAAO;AAAA;AAE/C,WAAO;AAAA;AAAA,EAGT,wBAAwB;AACtB,QAAI,SAAS;AAEb,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AACrD,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AAErD,WAAO;AAAA;AAAA,EAGT,cAAc;AACZ,WAAO,KAAK,IAAI,cAAc,WAAW;AAAA;AAAA,EAU3C,UAAU,KAAK,cAAc,YAAY;AACvC,UAAM,EAAE,YAAY,YAAY;AAChC,UAAM,YAAY,KAAK,UAAU,QAAQ,aAAa,QAAQ;AAC9D,UAAM,qBAAqB,KAAK,UAAU,QAAQ,sBAAsB,QAAQ;AAChF,UAAM,YAAY,KAAK;AACvB,QAAI,eAAe;AAEnB,QAAI,sBAAsB,YAAY;AACpC,YAAM,YAAY,IAAI,SAAS,OAAO,KAAK;AAC3C,UAAI;AACJ,UAAI,MAAM,QAAQ,aAAa;AAC7B,mBAAW,WAAW,IAAI,OAAG,kBAAkB,IAAI,KAAK;AAAA,aACnD;AACL,mBAAW,kBAAkB;AAAA;AAE/B,qBAAe,GAAG,aAAa;AAAA;AAEjC,UAAM,MAAM,IAAI,WAAW,QAAQ,eAAe,MAAM;AACxD,UAAM,MAAM,aAAa;AACzB,iBAAa;AACb,QAAI,CAAC,WAAW;AACd,WAAK,UAAU,IAAI,aAAa,OAAO;AAAA;AAEzC,WAAO,MAAM;AACX,YAAM,WAAW,YAAY;AAC7B,mBAAa;AACb,UAAI,WAAW;AACb,aAAK,UAAU,IAAI,UAAU,KAAK,QAAQ,WAAW;AAAA;AAAA;AAAA;AAAA,SA8CpD,eAAe,MAAM,gBAAgB,SAAS;AAcnD,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO;AAAA;AAIT,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,QAAI;AACJ,QAAI;AACJ,UAAM,aAAa,KAAK;AAExB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,gBAAgB,QAAQ;AAE9B,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,UAAU,gBAAgB,KAAK,IAAI,MAAM;AAC/C,UAAM,YAAY;AAClB,UAAM,aAAa;AAEnB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,QAAI;AACJ,UAAM,kBAAkB,WAAS;AAC/B,UAAI,OAAO,UAAU,eAAe,KAAK,SAAS,YAAY,QAAQ;AACpE,mBAAW,OAAO,WAAW,SAAS,WAAW;AACjD,YAAI,eAAe;AACjB,0BAAgB,GAAG,iBAAiB;AAAA,eAC/B;AACL,0BAAgB;AAAA;AAElB,mBAAW,iBAAiB;AAAA;AAAA;AAIhC,UAAM,sBAAsB;AAC5B,UAAM,kBAAkB,CAAC,MAAK,SAAS;AACrC,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,OAAM;AACpD,aAAK,QAAO,KAAI,OAAO,GAAG,KAAI,YAAY;AAAA;AAE5C,aAAO,KAAK;AAAA;AAGd,UAAM,sBAAsB;AAC5B,UAAM,kBAAkB,UAAO;AAC7B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,qBAAqB,OAAM;AACnE,cAAM,QAAQ,KAAI,YAAY;AAC9B,4BAAoB,QAAO,KAAI,OAAO,UAAU,KAAK,IAAI,QAAQ;AAAA;AAEnE,aAAO,oBAAoB;AAAA;AAG7B,UAAM,gBAAgB;AACtB,UAAM,YAAY,UAAO;AAEvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,eAAe,OAAM;AAC7D,cAAM,eAAe,gBAAgB,MAAK;AAC1C,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,eAAe,eAAe;AACtE,wBAAc,gBAAgB,eAAe,aAAa,MAAM,OAAO;AAAA;AAEzE,sBAAc,QAAO,cAAc;AAAA;AAErC,aAAO,cAAc;AAAA;AAGvB,UAAM,oBAAoB;AAC1B,UAAM,gBAAgB,UAAO;AAC3B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,mBAAmB,OAAM;AACjE,cAAM,UAAS,UAAU;AACzB,cAAM,UAAS,QAAO;AAEtB,0BAAkB,QAAO,CAAC,UAAS,KAAK,QAAO,UAAS;AAAA;AAE1D,aAAO,kBAAkB;AAAA;AAE3B,UAAM,yBAAyB,WAAS;AACtC,UAAI,uBAAsB,EAAE,MAAM,MAAM;AACxC,6BAAsB,qBACnB,OAAO,GAAG,qBAAoB,oBAC9B,IAAI,WAAS,EAAE,QAAQ,MAAM,YAAY,SAAO,IAAI,UAAU,QAC9D;AAEH,aAAO;AAAA;AAET,UAAM,YAAY,SAAO,eAAe,SAAS,IAAI,SAAS,SAAS;AACvE,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,SAAK,QAAQ,GAAG,QAAQ,YAAY,SAAS;AAC3C,YAAM,KAAK;AAGX,UAAI,UAAU,GAAG;AACf,eAAO,EAAE,OAAO,OAAO,KAAK,MAAM,UAAQ,CAAC,KAAK,MAAM,KAAK;AAC3D,oBAAY,KAAK;AAAA;AAGnB,UAAI,eAAe;AACjB,oBAAY;AAGZ,kBAAU,eAAe,MAAM,qBAAqB;AACpD,kBAAU;AACV,YAAI,YAAY,GAAG;AACjB,oBAAU,UAAU,IAAI,eAAe,MAAM,qBAAqB;AAAA,mBAE3D,UAAU,GAAG;AACpB,eAAK,KAAK,GAAG,KAAK,SAAS,MAAM;AAC/B,uBAAW,UAAU,IAAI,eAAe,MAAM,qBAAqB;AAAA;AAAA,mBAG9D,CAAC,EAAE,QAAQ,eAAe,MAAM,aAAa;AACpD,gCAAsB,uBAAuB,eAAe;AAC5D,eAAK,KAAK,GAAG,KAAK,oBAAoB,QAAQ,MAAM;AAClD,uBAAW,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAKzC,kBAAY,SAAS;AACrB,uBAAiB;AACjB,WAAK,OAAO,GAAG,OAAO,WAAW,QAAQ;AACvC,cAAM,KAAK;AAKX,2BAAmB,gBAAgB,KAAK;AACxC,qBAAa,UAAU;AAGvB,YAAI,UAAU,KAAK,CAAC,OAAO,UAAU,eAAe,KAAK,YAAY,MAAM;AACzE,cAAI,CAAC,WAAW,QAAQ;AACtB,uBAAW,OAAO,WAAW,MAAM;AAAA,iBAC9B;AACL,uBAAW;AACX,4BAAgB;AAChB,uBAAW,QAAQ;AAAA;AAAA;AAIvB,YAAI,mBAAmB,UAAa,mBAAmB,YAAY;AACjE,cAAI,eAAe;AAGjB,qBAAS,eAAe;AACxB,sBAAU;AACV,yBAAa;AAEb,gBAAI,QAAQ;AACV,mBAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,yBAAS,UAAU,GAAG,WAAW,eAAe,OAAO,eAAe;AACtE,uCAAuB,WAAW,QAAQ,MAAM;AAChD,0BAAU,qBAAqB;AAC/B,2BAAW;AACX,oBAAI,YAAY,GAAG;AACjB,8BAAY,UAAU,IAAI,GAAG,UAAU,qBAAqB;AAAA,2BAErD,UAAU,GAAG;AACpB,uBAAK,KAAK,GAAG,KAAK,SAAS,MAAM;AAC/B,gCAAY,UAAU,IAAI,GAAG,UAAU,qBAAqB;AAAA;AAAA,2BAGvD,CAAC,EAAE,QAAQ,WAAW,QAAQ,MAAM,aAAa;AACxD,wCAAsB,uBAAuB,WAAW,QAAQ;AAChE,uBAAK,KAAK,GAAG,KAAK,oBAAoB,QAAQ,MAAM;AAClD,gCAAY,IAAI,GAAG,UAAU,oBAAoB;AAAA;AAAA;AAGrD,oBAAI,CAAC,YAAY;AACf,+BAAa;AAAA;AAGf,2BAAW,aAAa;AACxB,0BAAU;AACV,oBAAI,IAAI,SAAS,GAAG;AAClB,+BAAa;AAAA;AAAA;AAAA,mBAGZ;AACL,yBAAW;AAAA;AAGb,gBAAI,aAAa,SAAS;AACxB,kBAAI,CAAC,UAAU,WAAW;AACxB,0BAAU,YAAY;AAAA,qBACjB;AACL,4BAAY;AAAA;AAAA,uBAEL,CAAC,UAAU,WAAW;AAC/B,wBAAU,UAAU;AACpB,+BAAiB,cAAc;AAE/B,kBAAI,WAAW,SAAS,YAAY,qBAAqB;AACvD,oBAAI,SAAS;AACX,0BAAQ,kBAAkB,UAAU,YAAY;AAAA;AAAA,qBAE7C;AACL,oBAAI,CAAC,QAAQ,iBAAiB;AAC5B,0BAAQ,kBAAkB;AAAA;AAE5B,wBAAQ,gBAAgB,KAAK,UAAU,YAAY;AAAA;AAAA;AAKvD,qBAAS;AAAA,iBACJ;AAIL,uBAAW;AACX,qBAAS,WAAW;AACpB,gBAAI,QAAQ;AACV,mBAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,oBAAI,MAAM,SAAS,GAAG;AACpB,2BAAS,SAAS,WAAW,MAAM;AAAA;AAErC,2BAAW,SAAS,WAAW,OAAO;AAAA;AAAA;AAAA;AAAA;AAO9C,eAAO,gBAAgB,QAAQ,IAAI;AACnC,kBAAU;AACV,yBAAiB;AACjB,+BAAuB;AAAA;AAGzB,UAAI,eAAe;AACjB,iBAAS,eAAe;AACxB,kBAAU;AACV,qBAAa;AAEb,YAAI,QAAQ;AACV,eAAK,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC3B,qBAAS,UAAU,GAAG,WAAW,eAAe,OAAO,eAAe;AACtE,mCAAuB,WAAW,QAAQ,MAAM;AAChD,sBAAU,qBAAqB;AAC/B,uBAAW;AACX,gBAAI,YAAY,GAAG;AACjB,0BAAY,UAAU,IAAI,GAAG,UAAU,qBAAqB;AAAA,uBAErD,UAAU,GAAG;AACpB,mBAAK,KAAK,GAAG,KAAK,SAAS,MAAM;AAC/B,4BAAY,UAAU,IAAI,GAAG,UAAU,qBAAqB;AAAA;AAAA,uBAGvD,CAAC,EAAE,QAAQ,WAAW,QAAQ,MAAM,aAAa;AACxD,oCAAsB,uBAAuB,WAAW,QAAQ;AAChE,mBAAK,KAAK,GAAG,KAAK,oBAAoB,QAAQ,MAAM;AAClD,4BAAY,IAAI,GAAG,UAAU,oBAAoB;AAAA;AAAA;AAGrD,gBAAI,CAAC,YAAY;AACf,2BAAa;AAAA;AAGf,uBAAW,aAAa;AACxB,sBAAU;AACV,gBAAI,IAAI,SAAS,GAAG;AAClB,2BAAa;AAAA;AAAA;AAAA,eAGZ;AACL,qBAAW;AAAA;AAGb,YAAI,aAAa,SAAS;AACxB,cAAI,CAAC,UAAU,WAAW;AACxB,sBAAU,YAAY;AAAA,iBACjB;AACL,wBAAY;AAAA;AAAA,mBAEL,CAAC,UAAU,WAAW;AAC/B,oBAAU,UAAU;AACpB,2BAAiB,cAAc;AAE/B,cAAI,WAAW,SAAS,YAAY,qBAAqB;AACvD,gBAAI,SAAS;AACX,sBAAQ,kBAAkB,UAAU,YAAY;AAAA;AAAA,iBAE7C;AACL,gBAAI,CAAC,QAAQ,iBAAiB;AAC5B,sBAAQ,kBAAkB;AAAA;AAE5B,oBAAQ,gBAAgB,KAAK,UAAU,YAAY;AAAA;AAAA;AAGvD,YAAI,CAAC,WAAW;AACd,kBAAQ,KAAK;AAAA;AAAA,aAEV;AACL,gBAAQ,SAAS;AAAA;AAAA;AAIrB,WAAO;AAAA;AAAA;AAIX,OAAO,UAAU;AACjB,OAAO,QAAQ,gBAAgB;AAC/B,OAAO,QAAQ,UAAU;", "names": []}