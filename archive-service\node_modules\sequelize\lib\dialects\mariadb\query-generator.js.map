{"version": 3, "sources": ["../../../src/dialects/mariadb/query-generator.js"], "sourcesContent": ["'use strict';\n\nconst MySQLQueryGenerator = require('../mysql/query-generator');\nconst Utils = require('./../../utils');\n\nclass MariaDBQueryGenerator extends MySQLQueryGenerator {\n  createSchema(schema, options) {\n    options = {\n      charset: null,\n      collate: null,\n      ...options\n    };\n\n    return Utils.joinSQLFragments([\n      'CREATE SCHEMA IF NOT EXISTS',\n      this.quoteIdentifier(schema),\n      options.charset && `DEFAULT CHARACTER SET ${this.escape(options.charset)}`,\n      options.collate && `DEFAULT COLLATE ${this.escape(options.collate)}`,\n      ';'\n    ]);\n  }\n\n  dropSchema(schema) {\n    return `DROP SCHEMA IF EXISTS ${this.quoteIdentifier(schema)};`;\n  }\n\n  showSchemasQuery(options) {\n    const schemasToSkip = [\n      '\\'MYSQL\\'',\n      '\\'INFORMATION_SCHEMA\\'',\n      '\\'PERFORMANCE_SCHEMA\\''\n    ];\n    if (options.skip && Array.isArray(options.skip) && options.skip.length > 0) {\n      for (const schemaName of options.skip) {\n        schemasToSkip.push(this.escape(schemaName));\n      }\n    }\n    return Utils.joinSQLFragments([\n      'SELECT SCHEMA_NAME as schema_name',\n      'FROM INFORMATION_SCHEMA.SCHEMATA',\n      `WHERE SCHEMA_NAME NOT IN (${schemasToSkip.join(', ')})`,\n      ';'\n    ]);\n  }\n\n  showTablesQuery(database) {\n    let query = 'SELECT TABLE_NAME, TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = \\'BASE TABLE\\'';\n    if (database) {\n      query += ` AND TABLE_SCHEMA = ${this.escape(database)}`;\n    } else {\n      query += ' AND TABLE_SCHEMA NOT IN (\\'MYSQL\\', \\'INFORMATION_SCHEMA\\', \\'PERFORMANCE_SCHEMA\\')';\n    }\n    return `${query};`;\n  }\n\n  /**\n   * Quote identifier in sql clause\n   *\n   * @param {string} identifier\n   * @param {boolean} force\n   *\n   * @returns {string}\n   */\n  quoteIdentifier(identifier, force) {\n    return Utils.addTicks(Utils.removeTicks(identifier, '`'), '`');\n  }\n}\n\nmodule.exports = MariaDBQueryGenerator;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,sBAAsB,QAAQ;AACpC,MAAM,QAAQ,QAAQ;AAEtB,oCAAoC,oBAAoB;AAAA,EACtD,aAAa,QAAQ,SAAS;AAC5B,cAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,OACN;AAGL,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,QAAQ,WAAW,yBAAyB,KAAK,OAAO,QAAQ;AAAA,MAChE,QAAQ,WAAW,mBAAmB,KAAK,OAAO,QAAQ;AAAA,MAC1D;AAAA;AAAA;AAAA,EAIJ,WAAW,QAAQ;AACjB,WAAO,yBAAyB,KAAK,gBAAgB;AAAA;AAAA,EAGvD,iBAAiB,SAAS;AACxB,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA;AAEF,QAAI,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,SAAS,QAAQ,KAAK,SAAS,GAAG;AAC1E,iBAAW,cAAc,QAAQ,MAAM;AACrC,sBAAc,KAAK,KAAK,OAAO;AAAA;AAAA;AAGnC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,6BAA6B,cAAc,KAAK;AAAA,MAChD;AAAA;AAAA;AAAA,EAIJ,gBAAgB,UAAU;AACxB,QAAI,QAAQ;AACZ,QAAI,UAAU;AACZ,eAAS,uBAAuB,KAAK,OAAO;AAAA,WACvC;AACL,eAAS;AAAA;AAEX,WAAO,GAAG;AAAA;AAAA,EAWZ,gBAAgB,YAAY,OAAO;AACjC,WAAO,MAAM,SAAS,MAAM,YAAY,YAAY,MAAM;AAAA;AAAA;AAI9D,OAAO,UAAU;", "names": []}