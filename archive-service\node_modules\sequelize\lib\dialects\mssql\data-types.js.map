{"version": 3, "sources": ["../../../src/dialects/mssql/data-types.js"], "sourcesContent": ["'use strict';\n\nconst moment = require('moment');\n\nmodule.exports = BaseTypes => {\n  const warn = BaseTypes.ABSTRACT.warn.bind(undefined, 'https://msdn.microsoft.com/en-us/library/ms187752%28v=sql.110%29.aspx');\n\n  /**\n   * Removes unsupported MSSQL options, i.e., LENGTH, UNSIGNED and ZEROFILL, for the integer data types.\n   *\n   * @param {object} dataType The base integer data type.\n   * @private\n   */\n  function removeUnsupportedIntegerOptions(dataType) {\n    if (dataType._length || dataType.options.length || dataType._unsigned || dataType._zerofill) {\n      warn(`MSSQL does not support '${dataType.key}' with options. Plain '${dataType.key}' will be used instead.`);\n      dataType._length = undefined;\n      dataType.options.length = undefined;\n      dataType._unsigned = undefined;\n      dataType._zerofill = undefined;\n    }\n  }\n\n  /**\n   * types: [hex, ...]\n   *\n   * @see hex here https://github.com/tediousjs/tedious/blob/master/src/data-type.ts\n   */\n\n  BaseTypes.DATE.types.mssql = [43];\n  BaseTypes.STRING.types.mssql = [231, 173];\n  BaseTypes.CHAR.types.mssql = [175];\n  BaseTypes.TEXT.types.mssql = false;\n  // https://msdn.microsoft.com/en-us/library/ms187745(v=sql.110).aspx\n  BaseTypes.TINYINT.types.mssql = [30];\n  BaseTypes.SMALLINT.types.mssql = [34];\n  BaseTypes.MEDIUMINT.types.mssql = false;\n  BaseTypes.INTEGER.types.mssql = [38];\n  BaseTypes.BIGINT.types.mssql = false;\n  BaseTypes.FLOAT.types.mssql = [109];\n  BaseTypes.TIME.types.mssql = [41];\n  BaseTypes.DATEONLY.types.mssql = [40];\n  BaseTypes.BOOLEAN.types.mssql = [104];\n  BaseTypes.BLOB.types.mssql = [165];\n  BaseTypes.DECIMAL.types.mssql = [106];\n  BaseTypes.UUID.types.mssql = false;\n  BaseTypes.ENUM.types.mssql = false;\n  BaseTypes.REAL.types.mssql = [109];\n  BaseTypes.DOUBLE.types.mssql = [109];\n  // BaseTypes.GEOMETRY.types.mssql = [240]; // not yet supported\n  BaseTypes.GEOMETRY.types.mssql = false;\n\n  class BLOB extends BaseTypes.BLOB {\n    toSql() {\n      if (this._length) {\n        if (this._length.toLowerCase() === 'tiny') { // tiny = 2^8\n          warn('MSSQL does not support BLOB with the `length` = `tiny` option. `VARBINARY(256)` will be used instead.');\n          return 'VARBINARY(256)';\n        }\n        warn('MSSQL does not support BLOB with the `length` option. `VARBINARY(MAX)` will be used instead.');\n      }\n      return 'VARBINARY(MAX)';\n    }\n    _hexify(hex) {\n      return `0x${hex}`;\n    }\n  }\n\n\n  class STRING extends BaseTypes.STRING {\n    toSql() {\n      if (!this._binary) {\n        return `NVARCHAR(${this._length})`;\n      }\n      return `BINARY(${this._length})`;\n    }\n    _stringify(value, options) {\n      if (this._binary) {\n        return BLOB.prototype._stringify(value);\n      }\n      return options.escape(value);\n    }\n    _bindParam(value, options) {\n      return options.bindParam(this._binary ? Buffer.from(value) : value);\n    }\n  }\n\n  STRING.prototype.escape = false;\n\n  class TEXT extends BaseTypes.TEXT {\n    toSql() {\n      // TEXT is deprecated in mssql and it would normally be saved as a non-unicode string.\n      // Using unicode is just future proof\n      if (this._length) {\n        if (this._length.toLowerCase() === 'tiny') { // tiny = 2^8\n          warn('MSSQL does not support TEXT with the `length` = `tiny` option. `NVARCHAR(256)` will be used instead.');\n          return 'NVARCHAR(256)';\n        }\n        warn('MSSQL does not support TEXT with the `length` option. `NVARCHAR(MAX)` will be used instead.');\n      }\n      return 'NVARCHAR(MAX)';\n    }\n  }\n\n  class BOOLEAN extends BaseTypes.BOOLEAN {\n    toSql() {\n      return 'BIT';\n    }\n  }\n\n  class UUID extends BaseTypes.UUID {\n    toSql() {\n      return 'CHAR(36)';\n    }\n  }\n\n  class NOW extends BaseTypes.NOW {\n    toSql() {\n      return 'GETDATE()';\n    }\n  }\n\n  class DATE extends BaseTypes.DATE {\n    toSql() {\n      return 'DATETIMEOFFSET';\n    }\n  }\n\n  class DATEONLY extends BaseTypes.DATEONLY {\n    static parse(value) {\n      return moment(value).format('YYYY-MM-DD');\n    }\n  }\n\n  class INTEGER extends BaseTypes.INTEGER {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  class TINYINT extends BaseTypes.TINYINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  class SMALLINT extends BaseTypes.SMALLINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  class BIGINT extends BaseTypes.BIGINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n  class REAL extends BaseTypes.REAL {\n    constructor(length, decimals) {\n      super(length, decimals);\n      // MSSQL does not support any options for real\n      if (this._length || this.options.length || this._unsigned || this._zerofill) {\n        warn('MSSQL does not support REAL with options. Plain `REAL` will be used instead.');\n        this._length = undefined;\n        this.options.length = undefined;\n        this._unsigned = undefined;\n        this._zerofill = undefined;\n      }\n    }\n  }\n  class FLOAT extends BaseTypes.FLOAT {\n    constructor(length, decimals) {\n      super(length, decimals);\n      // MSSQL does only support lengths as option.\n      // Values between 1-24 result in 7 digits precision (4 bytes storage size)\n      // Values between 25-53 result in 15 digits precision (8 bytes storage size)\n      // If decimals are provided remove these and print a warning\n      if (this._decimals) {\n        warn('MSSQL does not support Float with decimals. Plain `FLOAT` will be used instead.');\n        this._length = undefined;\n        this.options.length = undefined;\n      }\n      if (this._unsigned) {\n        warn('MSSQL does not support Float unsigned. `UNSIGNED` was removed.');\n        this._unsigned = undefined;\n      }\n      if (this._zerofill) {\n        warn('MSSQL does not support Float zerofill. `ZEROFILL` was removed.');\n        this._zerofill = undefined;\n      }\n    }\n  }\n  class ENUM extends BaseTypes.ENUM {\n    toSql() {\n      return 'VARCHAR(255)';\n    }\n  }\n\n  return {\n    BLOB,\n    BOOLEAN,\n    ENUM,\n    STRING,\n    UUID,\n    DATE,\n    DATEONLY,\n    NOW,\n    TINYINT,\n    SMALLINT,\n    INTEGER,\n    BIGINT,\n    REAL,\n    FLOAT,\n    TEXT\n  };\n};\n"], "mappings": ";AAEA,MAAM,SAAS,QAAQ;AAEvB,OAAO,UAAU,eAAa;AAC5B,QAAM,OAAO,UAAU,SAAS,KAAK,KAAK,QAAW;AAQrD,2CAAyC,UAAU;AACjD,QAAI,SAAS,WAAW,SAAS,QAAQ,UAAU,SAAS,aAAa,SAAS,WAAW;AAC3F,WAAK,2BAA2B,SAAS,6BAA6B,SAAS;AAC/E,eAAS,UAAU;AACnB,eAAS,QAAQ,SAAS;AAC1B,eAAS,YAAY;AACrB,eAAS,YAAY;AAAA;AAAA;AAUzB,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,OAAO,MAAM,QAAQ,CAAC,KAAK;AACrC,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,KAAK,MAAM,QAAQ;AAE7B,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,SAAS,MAAM,QAAQ,CAAC;AAClC,YAAU,UAAU,MAAM,QAAQ;AAClC,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,OAAO,MAAM,QAAQ;AAC/B,YAAU,MAAM,MAAM,QAAQ,CAAC;AAC/B,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,SAAS,MAAM,QAAQ,CAAC;AAClC,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,KAAK,MAAM,QAAQ;AAC7B,YAAU,KAAK,MAAM,QAAQ;AAC7B,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,OAAO,MAAM,QAAQ,CAAC;AAEhC,YAAU,SAAS,MAAM,QAAQ;AAEjC,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,YAAI,KAAK,QAAQ,kBAAkB,QAAQ;AACzC,eAAK;AACL,iBAAO;AAAA;AAET,aAAK;AAAA;AAEP,aAAO;AAAA;AAAA,IAET,QAAQ,KAAK;AACX,aAAO,KAAK;AAAA;AAAA;AAKhB,uBAAqB,UAAU,OAAO;AAAA,IACpC,QAAQ;AACN,UAAI,CAAC,KAAK,SAAS;AACjB,eAAO,YAAY,KAAK;AAAA;AAE1B,aAAO,UAAU,KAAK;AAAA;AAAA,IAExB,WAAW,OAAO,SAAS;AACzB,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,UAAU,WAAW;AAAA;AAEnC,aAAO,QAAQ,OAAO;AAAA;AAAA,IAExB,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,UAAU,KAAK,UAAU,OAAO,KAAK,SAAS;AAAA;AAAA;AAIjE,SAAO,UAAU,SAAS;AAE1B,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AAGN,UAAI,KAAK,SAAS;AAChB,YAAI,KAAK,QAAQ,kBAAkB,QAAQ;AACzC,eAAK;AACL,iBAAO;AAAA;AAET,aAAK;AAAA;AAEP,aAAO;AAAA;AAAA;AAIX,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,oBAAkB,UAAU,IAAI;AAAA,IAC9B,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,yBAAuB,UAAU,SAAS;AAAA,WACjC,MAAM,OAAO;AAClB,aAAO,OAAO,OAAO,OAAO;AAAA;AAAA;AAIhC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAGpC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAGpC,yBAAuB,UAAU,SAAS;AAAA,IACxC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAGpC,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAGpC,qBAAmB,UAAU,KAAK;AAAA,IAChC,YAAY,QAAQ,UAAU;AAC5B,YAAM,QAAQ;AAEd,UAAI,KAAK,WAAW,KAAK,QAAQ,UAAU,KAAK,aAAa,KAAK,WAAW;AAC3E,aAAK;AACL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AACtB,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA;AAAA;AAAA;AAIvB,sBAAoB,UAAU,MAAM;AAAA,IAClC,YAAY,QAAQ,UAAU;AAC5B,YAAM,QAAQ;AAKd,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AAAA;AAExB,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,YAAY;AAAA;AAEnB,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,YAAY;AAAA;AAAA;AAAA;AAIvB,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;", "names": []}