const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');

// Store untuk tracking rate limit per user
const userLimitStore = new Map();

/**
 * Custom key generator yang menggunakan user ID jika tersedia, atau IP address
 */
const keyGenerator = (req) => {
  if (req.user && req.user.id) {
    return `user:${req.user.id}`;
  }
  return `ip:${req.ip}`;
};

/**
 * Custom handler untuk rate limit exceeded
 */
const rateLimitHandler = (req, res) => {
  const key = keyGenerator(req);
  
  logger.warn(`Rate limit exceeded for ${key}`, {
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });

  res.status(429).json({
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests. Please try again later.',
      retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || 900000) / 1000)
    }
  });
};

/**
 * Rate limiter utama berdasarkan IP
 */
const generalRateLimit = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 200, // limit each IP to 100 requests per windowMs
  message: rateLimitHandler,
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req) => req.ip,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  }
});

/**
 * Rate limiter khusus untuk endpoint authentication
 */
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // limit each IP to 5 auth requests per windowMs
  message: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => req.ip,
  skipSuccessfulRequests: true // Don't count successful requests
});

/**
 * Rate limiter khusus untuk endpoint assessment (lebih ketat)
 */
const assessmentRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 500, // limit each user to 10 assessment submissions per hour
  message: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: keyGenerator,
  skip: (req) => {
    // Skip if not authenticated
    return !req.user;
  }
});

/**
 * Middleware untuk menambahkan rate limit headers
 */
const addRateLimitHeaders = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Add custom rate limit headers
    res.set({
      'X-RateLimit-Window': process.env.RATE_LIMIT_WINDOW_MS || '900000',
      'X-RateLimit-Policy': 'general'
    });
    
    return originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  generalRateLimit,
  authRateLimit,
  assessmentRateLimit,
  addRateLimitHeaders
};
