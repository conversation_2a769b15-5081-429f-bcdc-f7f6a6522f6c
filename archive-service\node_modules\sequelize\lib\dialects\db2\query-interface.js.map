{"version": 3, "sources": ["../../../src/dialects/db2/query-interface.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst Utils = require('../../utils');\nconst Op = require('../../operators');\nconst { QueryInterface } = require('../abstract/query-interface');\nconst QueryTypes = require('../../query-types');\n\n/**\n * The interface that <PERSON><PERSON><PERSON> uses to talk with Db2 database\n */\nclass Db2QueryInterface extends QueryInterface {\n  async getForeignKeyReferencesForTable(tableName, options) {\n    const queryOptions = {\n      ...options,\n      type: QueryTypes.FOREIGNKEYS\n    };\n    const query = this.queryGenerator.getForeignKeysQuery(tableName, this.sequelize.config.username.toUpperCase());\n    return this.sequelize.query(query, queryOptions);\n  }\n\n  async upsert(tableName, insertValues, updateValues, where, options) {\n    options = { ...options };\n\n    const model = options.model;\n    const wheres = [];\n    const attributes = Object.keys(insertValues);\n    let indexes = [];\n    let indexFields;\n\n    options = _.clone(options);\n\n    if (!Utils.isWhereEmpty(where)) {\n      wheres.push(where);\n    }\n\n    // Lets combine unique keys and indexes into one\n    indexes = _.map(model.uniqueKeys, value => {\n      return value.fields;\n    });\n\n    model._indexes.forEach(value => {\n      if (value.unique) {\n        // fields in the index may both the strings or objects with an attribute property - lets sanitize that\n        indexFields = value.fields.map(field => {\n          if (_.isPlainObject(field)) {\n            return field.attribute;\n          }\n          return field;\n        });\n        indexes.push(indexFields);\n      }\n    });\n\n    for (const index of indexes) {\n      if (_.intersection(attributes, index).length === index.length) {\n        where = {};\n        for (const field of index) {\n          where[field] = insertValues[field];\n        }\n        wheres.push(where);\n      }\n    }\n\n    where = { [Op.or]: wheres };\n\n    options.type = QueryTypes.UPSERT;\n    options.raw = true;\n\n    const sql = this.queryGenerator.upsertQuery(tableName, insertValues, updateValues, where, model, options);\n    const result = await this.sequelize.query(sql, options);\n    return [result, undefined];\n  }\n\n  async createTable(tableName, attributes, options, model) {\n    let sql = '';\n\n    options = { ...options };\n\n    if (options && options.uniqueKeys) {\n      _.forOwn(options.uniqueKeys, uniqueKey => {\n        if (uniqueKey.customIndex === undefined) {\n          uniqueKey.customIndex = true;\n        }\n      });\n    }\n\n    if (model) {\n      options.uniqueKeys = options.uniqueKeys || model.uniqueKeys;\n    }\n    attributes = _.mapValues(\n      attributes,\n      attribute => this.sequelize.normalizeAttribute(attribute)\n    );  \n    if (options.indexes) {\n      options.indexes.forEach(fields=>{\n        const fieldArr = fields.fields;\n        if (fieldArr.length === 1) {\n          fieldArr.forEach(field=>{       \n            for (const property in attributes) {\n              if (field === attributes[property].field) {\n                attributes[property].unique = true;\n              }\n            }\n          });\n        }\n      });\n    }\n    if (options.alter) {\n      if (options.indexes) {\n        options.indexes.forEach(fields=>{\n          const fieldArr = fields.fields;\n          if (fieldArr.length === 1) {\n            fieldArr.forEach(field=>{       \n              for (const property in attributes) {\n                if (field === attributes[property].field && attributes[property].unique) {\n                  attributes[property].unique = false;\n                }\n              }\n            });\n          }\n        });\n      }\n    }\n\n    if (\n      !tableName.schema &&\n      (options.schema || !!model && model._schema)\n    ) {\n      tableName = this.queryGenerator.addSchema({\n        tableName,\n        _schema: !!model && model._schema || options.schema\n      });\n    }\n\n    attributes = this.queryGenerator.attributesToSQL(attributes, { table: tableName, context: 'createTable', withoutForeignKeyConstraints: options.withoutForeignKeyConstraints });\n    sql = this.queryGenerator.createTableQuery(tableName, attributes, options);\n\n    return await this.sequelize.query(sql, options);\n  }\n\n}\n\nexports.Db2QueryInterface = Db2QueryInterface;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,QAAQ,QAAQ;AACtB,MAAM,KAAK,QAAQ;AACnB,MAAM,EAAE,mBAAmB,QAAQ;AACnC,MAAM,aAAa,QAAQ;AAK3B,gCAAgC,eAAe;AAAA,QACvC,gCAAgC,WAAW,SAAS;AACxD,UAAM,eAAe,iCAChB,UADgB;AAAA,MAEnB,MAAM,WAAW;AAAA;AAEnB,UAAM,QAAQ,KAAK,eAAe,oBAAoB,WAAW,KAAK,UAAU,OAAO,SAAS;AAChG,WAAO,KAAK,UAAU,MAAM,OAAO;AAAA;AAAA,QAG/B,OAAO,WAAW,cAAc,cAAc,OAAO,SAAS;AAClE,cAAU,mBAAK;AAEf,UAAM,QAAQ,QAAQ;AACtB,UAAM,SAAS;AACf,UAAM,aAAa,OAAO,KAAK;AAC/B,QAAI,UAAU;AACd,QAAI;AAEJ,cAAU,EAAE,MAAM;AAElB,QAAI,CAAC,MAAM,aAAa,QAAQ;AAC9B,aAAO,KAAK;AAAA;AAId,cAAU,EAAE,IAAI,MAAM,YAAY,WAAS;AACzC,aAAO,MAAM;AAAA;AAGf,UAAM,SAAS,QAAQ,WAAS;AAC9B,UAAI,MAAM,QAAQ;AAEhB,sBAAc,MAAM,OAAO,IAAI,WAAS;AACtC,cAAI,EAAE,cAAc,QAAQ;AAC1B,mBAAO,MAAM;AAAA;AAEf,iBAAO;AAAA;AAET,gBAAQ,KAAK;AAAA;AAAA;AAIjB,eAAW,SAAS,SAAS;AAC3B,UAAI,EAAE,aAAa,YAAY,OAAO,WAAW,MAAM,QAAQ;AAC7D,gBAAQ;AACR,mBAAW,SAAS,OAAO;AACzB,gBAAM,SAAS,aAAa;AAAA;AAE9B,eAAO,KAAK;AAAA;AAAA;AAIhB,YAAQ,GAAG,GAAG,KAAK;AAEnB,YAAQ,OAAO,WAAW;AAC1B,YAAQ,MAAM;AAEd,UAAM,MAAM,KAAK,eAAe,YAAY,WAAW,cAAc,cAAc,OAAO,OAAO;AACjG,UAAM,SAAS,MAAM,KAAK,UAAU,MAAM,KAAK;AAC/C,WAAO,CAAC,QAAQ;AAAA;AAAA,QAGZ,YAAY,WAAW,YAAY,SAAS,OAAO;AACvD,QAAI,MAAM;AAEV,cAAU,mBAAK;AAEf,QAAI,WAAW,QAAQ,YAAY;AACjC,QAAE,OAAO,QAAQ,YAAY,eAAa;AACxC,YAAI,UAAU,gBAAgB,QAAW;AACvC,oBAAU,cAAc;AAAA;AAAA;AAAA;AAK9B,QAAI,OAAO;AACT,cAAQ,aAAa,QAAQ,cAAc,MAAM;AAAA;AAEnD,iBAAa,EAAE,UACb,YACA,eAAa,KAAK,UAAU,mBAAmB;AAEjD,QAAI,QAAQ,SAAS;AACnB,cAAQ,QAAQ,QAAQ,YAAQ;AAC9B,cAAM,WAAW,OAAO;AACxB,YAAI,SAAS,WAAW,GAAG;AACzB,mBAAS,QAAQ,WAAO;AACtB,uBAAW,YAAY,YAAY;AACjC,kBAAI,UAAU,WAAW,UAAU,OAAO;AACxC,2BAAW,UAAU,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAO1C,QAAI,QAAQ,OAAO;AACjB,UAAI,QAAQ,SAAS;AACnB,gBAAQ,QAAQ,QAAQ,YAAQ;AAC9B,gBAAM,WAAW,OAAO;AACxB,cAAI,SAAS,WAAW,GAAG;AACzB,qBAAS,QAAQ,WAAO;AACtB,yBAAW,YAAY,YAAY;AACjC,oBAAI,UAAU,WAAW,UAAU,SAAS,WAAW,UAAU,QAAQ;AACvE,6BAAW,UAAU,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAS5C,QACE,CAAC,UAAU,UACV,SAAQ,UAAU,CAAC,CAAC,SAAS,MAAM,UACpC;AACA,kBAAY,KAAK,eAAe,UAAU;AAAA,QACxC;AAAA,QACA,SAAS,CAAC,CAAC,SAAS,MAAM,WAAW,QAAQ;AAAA;AAAA;AAIjD,iBAAa,KAAK,eAAe,gBAAgB,YAAY,EAAE,OAAO,WAAW,SAAS,eAAe,8BAA8B,QAAQ;AAC/I,UAAM,KAAK,eAAe,iBAAiB,WAAW,YAAY;AAElE,WAAO,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA;AAAA;AAK3C,QAAQ,oBAAoB;", "names": []}