{"version": 3, "sources": ["../../../src/dialects/abstract/connection-manager.js"], "sourcesContent": ["'use strict';\n\nconst { Pool, TimeoutError } = require('sequelize-pool');\nconst _ = require('lodash');\nconst semver = require('semver');\nconst errors = require('../../errors');\nconst { logger } = require('../../utils/logger');\nconst deprecations = require('../../utils/deprecations');\nconst debug = logger.debugContext('pool');\n\n/**\n * Abstract Connection Manager\n *\n * Connection manager which handles pooling & replication.\n * Uses sequelize-pool for pooling\n *\n * @private\n */\nclass ConnectionManager {\n  constructor(dialect, sequelize) {\n    const config = _.cloneDeep(sequelize.config);\n\n    this.sequelize = sequelize;\n    this.config = config;\n    this.dialect = dialect;\n    this.versionPromise = null;\n    this.dialectName = this.sequelize.options.dialect;\n\n    if (config.pool === false) {\n      throw new Error('Support for pool:false was removed in v4.0');\n    }\n\n    config.pool = _.defaults(config.pool || {}, {\n      max: 5,\n      min: 0,\n      idle: 10000,\n      acquire: 60000,\n      evict: 1000,\n      validate: this._validate.bind(this)\n    });\n\n    this.initPools();\n  }\n\n  refreshTypeParser(dataTypes) {\n    _.each(dataTypes, dataType => {\n      if (Object.prototype.hasOwnProperty.call(dataType, 'parse')) {\n        if (dataType.types[this.dialectName]) {\n          this._refreshTypeParser(dataType);\n        } else {\n          throw new Error(`Parse function not supported for type ${dataType.key} in dialect ${this.dialectName}`);\n        }\n      }\n    });\n  }\n\n  /**\n   * Try to load dialect module from various configured options.\n   * Priority goes like dialectModulePath > dialectModule > require(default)\n   *\n   * @param {string} moduleName Name of dialect module to lookup\n   *\n   * @private\n   * @returns {object}\n   */\n  _loadDialectModule(moduleName) {\n    try {\n      if (this.sequelize.config.dialectModulePath) {\n        return require(this.sequelize.config.dialectModulePath);\n      }\n      if (this.sequelize.config.dialectModule) {\n        return this.sequelize.config.dialectModule;\n      }\n      return require(moduleName);\n\n    } catch (err) {\n      if (err.code === 'MODULE_NOT_FOUND') {\n        if (this.sequelize.config.dialectModulePath) {\n          throw new Error(`Unable to find dialect at ${this.sequelize.config.dialectModulePath}`);\n        }\n        throw new Error(`Please install ${moduleName} package manually`);\n      }\n\n      throw err;\n    }\n  }\n\n  /**\n   * Handler which executes on process exit or connection manager shutdown\n   *\n   * @private\n   * @returns {Promise}\n   */\n  async _onProcessExit() {\n    if (!this.pool) {\n      return;\n    }\n\n    await this.pool.drain();\n    debug('connection drain due to process exit');\n\n    return await this.pool.destroyAllNow();\n  }\n\n  /**\n   * Drain the pool and close it permanently\n   *\n   * @returns {Promise}\n   */\n  async close() {\n    // Mark close of pool\n    this.getConnection = async function getConnection() {\n      throw new Error('ConnectionManager.getConnection was called after the connection manager was closed!');\n    };\n\n    return await this._onProcessExit();\n  }\n\n  /**\n   * Initialize connection pool. By default pool autostart is set to false, so no connection will be\n   * be created unless `pool.acquire` is called.\n   */\n  initPools() {\n    const config = this.config;\n\n    if (!config.replication) {\n      this.pool = new Pool({\n        name: 'sequelize',\n        create: () => this._connect(config),\n        destroy: async connection => {\n          const result = await this._disconnect(connection);\n          debug('connection destroy');\n          return result;\n        },\n        validate: config.pool.validate,\n        max: config.pool.max,\n        min: config.pool.min,\n        acquireTimeoutMillis: config.pool.acquire,\n        idleTimeoutMillis: config.pool.idle,\n        reapIntervalMillis: config.pool.evict,\n        maxUses: config.pool.maxUses\n      });\n\n      debug(`pool created with max/min: ${config.pool.max}/${config.pool.min}, no replication`);\n\n      return;\n    }\n\n    if (!Array.isArray(config.replication.read)) {\n      config.replication.read = [config.replication.read];\n    }\n\n    // Map main connection config\n    config.replication.write = _.defaults(config.replication.write, _.omit(config, 'replication'));\n\n    // Apply defaults to each read config\n    config.replication.read = config.replication.read.map(readConfig =>\n      _.defaults(readConfig, _.omit(this.config, 'replication'))\n    );\n\n    // custom pooling for replication (original author @janmeier)\n    let reads = 0;\n    this.pool = {\n      release: client => {\n        if (client.queryType === 'read') {\n          this.pool.read.release(client);\n        } else {\n          this.pool.write.release(client);\n        }\n      },\n      acquire: (queryType, useMaster) => {\n        useMaster = useMaster === undefined ? false : useMaster;\n        if (queryType === 'SELECT' && !useMaster) {\n          return this.pool.read.acquire();\n        }\n        return this.pool.write.acquire();\n      },\n      destroy: connection => {\n        this.pool[connection.queryType].destroy(connection);\n        debug('connection destroy');\n      },\n      destroyAllNow: async () => {\n        await Promise.all([\n          this.pool.read.destroyAllNow(),\n          this.pool.write.destroyAllNow()\n        ]);\n\n        debug('all connections destroyed');\n      },\n      drain: async () => Promise.all([\n        this.pool.write.drain(),\n        this.pool.read.drain()\n      ]),\n      read: new Pool({\n        name: 'sequelize:read',\n        create: async () => {\n          // round robin config\n          const nextRead = reads++ % config.replication.read.length;\n          const connection = await this._connect(config.replication.read[nextRead]);\n          connection.queryType = 'read';\n          return connection;\n        },\n        destroy: connection => this._disconnect(connection),\n        validate: config.pool.validate,\n        max: config.pool.max,\n        min: config.pool.min,\n        acquireTimeoutMillis: config.pool.acquire,\n        idleTimeoutMillis: config.pool.idle,\n        reapIntervalMillis: config.pool.evict,\n        maxUses: config.pool.maxUses\n      }),\n      write: new Pool({\n        name: 'sequelize:write',\n        create: async () => {\n          const connection = await this._connect(config.replication.write);\n          connection.queryType = 'write';\n          return connection;\n        },\n        destroy: connection => this._disconnect(connection),\n        validate: config.pool.validate,\n        max: config.pool.max,\n        min: config.pool.min,\n        acquireTimeoutMillis: config.pool.acquire,\n        idleTimeoutMillis: config.pool.idle,\n        reapIntervalMillis: config.pool.evict,\n        maxUses: config.pool.maxUses\n      })\n    };\n\n    debug(`pool created with max/min: ${config.pool.max}/${config.pool.min}, with replication`);\n  }\n\n  /**\n   * Get connection from pool. It sets database version if it's not already set.\n   * Call pool.acquire to get a connection\n   *\n   * @param {object}   [options]                 Pool options\n   * @param {string}   [options.type]            Set which replica to use. Available options are `read` and `write`\n   * @param {boolean}  [options.useMaster=false] Force master or write replica to get connection from\n   *\n   * @returns {Promise<Connection>}\n   */\n  async getConnection(options) {\n    options = options || {};\n\n    if (this.sequelize.options.databaseVersion === 0) {\n      if (!this.versionPromise) {\n        this.versionPromise = (async () => {\n          try {\n            const connection = await this._connect(this.config.replication.write || this.config);\n            const _options = {};\n\n            _options.transaction = { connection }; // Cheat .query to use our private connection\n            _options.logging = () => {};\n            _options.logging.__testLoggingFn = true;\n\n            //connection might have set databaseVersion value at initialization,\n            //avoiding a useless round trip\n            if (this.sequelize.options.databaseVersion === 0) {\n              const version = await this.sequelize.databaseVersion(_options);\n              const parsedVersion = _.get(semver.coerce(version), 'version') || version;\n              this.sequelize.options.databaseVersion = semver.valid(parsedVersion)\n                ? parsedVersion\n                : this.dialect.defaultVersion;\n            }\n\n            if (semver.lt(this.sequelize.options.databaseVersion, this.dialect.defaultVersion)) {\n              deprecations.unsupportedEngine();\n              debug(`Unsupported database engine version ${this.sequelize.options.databaseVersion}`);\n            }\n\n            this.versionPromise = null;\n            return await this._disconnect(connection);\n          } catch (err) {\n            this.versionPromise = null;\n            throw err;\n          }\n        })();\n      }\n      await this.versionPromise;\n    }\n\n    let result;\n\n    try {\n\n      await this.sequelize.runHooks('beforePoolAcquire', options);\n\n      result = await this.pool.acquire(options.type, options.useMaster);\n\n      await this.sequelize.runHooks('afterPoolAcquire', result, options);\n\n    } catch (error) {\n      if (error instanceof TimeoutError) throw new errors.ConnectionAcquireTimeoutError(error);\n      throw error;\n    }\n\n    debug('connection acquired');\n\n    return result;\n  }\n\n  /**\n   * Release a pooled connection so it can be utilized by other connection requests\n   *\n   * @param {Connection} connection\n   */\n  releaseConnection(connection) {\n    this.pool.release(connection);\n    debug('connection released');\n  }\n\n  /**\n   * Destroys a pooled connection and removes it from the pool.\n   *\n   * @param {Connection} connection\n   */\n  async destroyConnection(connection) {\n    await this.pool.destroy(connection);\n    debug(`connection ${connection.uuid} destroyed`);\n  }\n\n  /**\n   * Call dialect library to get connection\n   *\n   * @param {*} config Connection config\n   * @private\n   * @returns {Promise<Connection>}\n   */\n  async _connect(config) {\n    await this.sequelize.runHooks('beforeConnect', config);\n    const connection = await this.dialect.connectionManager.connect(config);\n    await this.sequelize.runHooks('afterConnect', connection, config);\n    return connection;\n  }\n\n  /**\n   * Call dialect library to disconnect a connection\n   *\n   * @param {Connection} connection\n   * @private\n   * @returns {Promise}\n   */\n  async _disconnect(connection) {\n    await this.sequelize.runHooks('beforeDisconnect', connection);\n    await this.dialect.connectionManager.disconnect(connection);\n    return this.sequelize.runHooks('afterDisconnect', connection);\n  }\n\n  /**\n   * Determine if a connection is still valid or not\n   *\n   * @param {Connection} connection\n   *\n   * @returns {boolean}\n   */\n  _validate(connection) {\n    if (!this.dialect.connectionManager.validate) {\n      return true;\n    }\n\n    return this.dialect.connectionManager.validate(connection);\n  }\n}\n\nmodule.exports = ConnectionManager;\nmodule.exports.ConnectionManager = ConnectionManager;\nmodule.exports.default = ConnectionManager;\n"], "mappings": ";AAEA,MAAM,EAAE,MAAM,iBAAiB,QAAQ;AACvC,MAAM,IAAI,QAAQ;AAClB,MAAM,SAAS,QAAQ;AACvB,MAAM,SAAS,QAAQ;AACvB,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,eAAe,QAAQ;AAC7B,MAAM,QAAQ,OAAO,aAAa;AAUlC,wBAAwB;AAAA,EACtB,YAAY,SAAS,WAAW;AAC9B,UAAM,SAAS,EAAE,UAAU,UAAU;AAErC,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,cAAc,KAAK,UAAU,QAAQ;AAE1C,QAAI,OAAO,SAAS,OAAO;AACzB,YAAM,IAAI,MAAM;AAAA;AAGlB,WAAO,OAAO,EAAE,SAAS,OAAO,QAAQ,IAAI;AAAA,MAC1C,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,UAAU,KAAK,UAAU,KAAK;AAAA;AAGhC,SAAK;AAAA;AAAA,EAGP,kBAAkB,WAAW;AAC3B,MAAE,KAAK,WAAW,cAAY;AAC5B,UAAI,OAAO,UAAU,eAAe,KAAK,UAAU,UAAU;AAC3D,YAAI,SAAS,MAAM,KAAK,cAAc;AACpC,eAAK,mBAAmB;AAAA,eACnB;AACL,gBAAM,IAAI,MAAM,yCAAyC,SAAS,kBAAkB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAejG,mBAAmB,YAAY;AAC7B,QAAI;AACF,UAAI,KAAK,UAAU,OAAO,mBAAmB;AAC3C,eAAO,QAAQ,KAAK,UAAU,OAAO;AAAA;AAEvC,UAAI,KAAK,UAAU,OAAO,eAAe;AACvC,eAAO,KAAK,UAAU,OAAO;AAAA;AAE/B,aAAO,QAAQ;AAAA,aAER,KAAP;AACA,UAAI,IAAI,SAAS,oBAAoB;AACnC,YAAI,KAAK,UAAU,OAAO,mBAAmB;AAC3C,gBAAM,IAAI,MAAM,6BAA6B,KAAK,UAAU,OAAO;AAAA;AAErE,cAAM,IAAI,MAAM,kBAAkB;AAAA;AAGpC,YAAM;AAAA;AAAA;AAAA,QAUJ,iBAAiB;AACrB,QAAI,CAAC,KAAK,MAAM;AACd;AAAA;AAGF,UAAM,KAAK,KAAK;AAChB,UAAM;AAEN,WAAO,MAAM,KAAK,KAAK;AAAA;AAAA,QAQnB,QAAQ;AAEZ,SAAK,gBAAgB,+BAA+B;AAClD,YAAM,IAAI,MAAM;AAAA;AAGlB,WAAO,MAAM,KAAK;AAAA;AAAA,EAOpB,YAAY;AACV,UAAM,SAAS,KAAK;AAEpB,QAAI,CAAC,OAAO,aAAa;AACvB,WAAK,OAAO,IAAI,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,QAAQ,MAAM,KAAK,SAAS;AAAA,QAC5B,SAAS,OAAM,eAAc;AAC3B,gBAAM,SAAS,MAAM,KAAK,YAAY;AACtC,gBAAM;AACN,iBAAO;AAAA;AAAA,QAET,UAAU,OAAO,KAAK;AAAA,QACtB,KAAK,OAAO,KAAK;AAAA,QACjB,KAAK,OAAO,KAAK;AAAA,QACjB,sBAAsB,OAAO,KAAK;AAAA,QAClC,mBAAmB,OAAO,KAAK;AAAA,QAC/B,oBAAoB,OAAO,KAAK;AAAA,QAChC,SAAS,OAAO,KAAK;AAAA;AAGvB,YAAM,8BAA8B,OAAO,KAAK,OAAO,OAAO,KAAK;AAEnE;AAAA;AAGF,QAAI,CAAC,MAAM,QAAQ,OAAO,YAAY,OAAO;AAC3C,aAAO,YAAY,OAAO,CAAC,OAAO,YAAY;AAAA;AAIhD,WAAO,YAAY,QAAQ,EAAE,SAAS,OAAO,YAAY,OAAO,EAAE,KAAK,QAAQ;AAG/E,WAAO,YAAY,OAAO,OAAO,YAAY,KAAK,IAAI,gBACpD,EAAE,SAAS,YAAY,EAAE,KAAK,KAAK,QAAQ;AAI7C,QAAI,QAAQ;AACZ,SAAK,OAAO;AAAA,MACV,SAAS,YAAU;AACjB,YAAI,OAAO,cAAc,QAAQ;AAC/B,eAAK,KAAK,KAAK,QAAQ;AAAA,eAClB;AACL,eAAK,KAAK,MAAM,QAAQ;AAAA;AAAA;AAAA,MAG5B,SAAS,CAAC,WAAW,cAAc;AACjC,oBAAY,cAAc,SAAY,QAAQ;AAC9C,YAAI,cAAc,YAAY,CAAC,WAAW;AACxC,iBAAO,KAAK,KAAK,KAAK;AAAA;AAExB,eAAO,KAAK,KAAK,MAAM;AAAA;AAAA,MAEzB,SAAS,gBAAc;AACrB,aAAK,KAAK,WAAW,WAAW,QAAQ;AACxC,cAAM;AAAA;AAAA,MAER,eAAe,YAAY;AACzB,cAAM,QAAQ,IAAI;AAAA,UAChB,KAAK,KAAK,KAAK;AAAA,UACf,KAAK,KAAK,MAAM;AAAA;AAGlB,cAAM;AAAA;AAAA,MAER,OAAO,YAAY,QAAQ,IAAI;AAAA,QAC7B,KAAK,KAAK,MAAM;AAAA,QAChB,KAAK,KAAK,KAAK;AAAA;AAAA,MAEjB,MAAM,IAAI,KAAK;AAAA,QACb,MAAM;AAAA,QACN,QAAQ,YAAY;AAElB,gBAAM,WAAW,UAAU,OAAO,YAAY,KAAK;AACnD,gBAAM,aAAa,MAAM,KAAK,SAAS,OAAO,YAAY,KAAK;AAC/D,qBAAW,YAAY;AACvB,iBAAO;AAAA;AAAA,QAET,SAAS,gBAAc,KAAK,YAAY;AAAA,QACxC,UAAU,OAAO,KAAK;AAAA,QACtB,KAAK,OAAO,KAAK;AAAA,QACjB,KAAK,OAAO,KAAK;AAAA,QACjB,sBAAsB,OAAO,KAAK;AAAA,QAClC,mBAAmB,OAAO,KAAK;AAAA,QAC/B,oBAAoB,OAAO,KAAK;AAAA,QAChC,SAAS,OAAO,KAAK;AAAA;AAAA,MAEvB,OAAO,IAAI,KAAK;AAAA,QACd,MAAM;AAAA,QACN,QAAQ,YAAY;AAClB,gBAAM,aAAa,MAAM,KAAK,SAAS,OAAO,YAAY;AAC1D,qBAAW,YAAY;AACvB,iBAAO;AAAA;AAAA,QAET,SAAS,gBAAc,KAAK,YAAY;AAAA,QACxC,UAAU,OAAO,KAAK;AAAA,QACtB,KAAK,OAAO,KAAK;AAAA,QACjB,KAAK,OAAO,KAAK;AAAA,QACjB,sBAAsB,OAAO,KAAK;AAAA,QAClC,mBAAmB,OAAO,KAAK;AAAA,QAC/B,oBAAoB,OAAO,KAAK;AAAA,QAChC,SAAS,OAAO,KAAK;AAAA;AAAA;AAIzB,UAAM,8BAA8B,OAAO,KAAK,OAAO,OAAO,KAAK;AAAA;AAAA,QAa/D,cAAc,SAAS;AAC3B,cAAU,WAAW;AAErB,QAAI,KAAK,UAAU,QAAQ,oBAAoB,GAAG;AAChD,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAkB,aAAY;AACjC,cAAI;AACF,kBAAM,aAAa,MAAM,KAAK,SAAS,KAAK,OAAO,YAAY,SAAS,KAAK;AAC7E,kBAAM,WAAW;AAEjB,qBAAS,cAAc,EAAE;AACzB,qBAAS,UAAU,MAAM;AAAA;AACzB,qBAAS,QAAQ,kBAAkB;AAInC,gBAAI,KAAK,UAAU,QAAQ,oBAAoB,GAAG;AAChD,oBAAM,UAAU,MAAM,KAAK,UAAU,gBAAgB;AACrD,oBAAM,gBAAgB,EAAE,IAAI,OAAO,OAAO,UAAU,cAAc;AAClE,mBAAK,UAAU,QAAQ,kBAAkB,OAAO,MAAM,iBAClD,gBACA,KAAK,QAAQ;AAAA;AAGnB,gBAAI,OAAO,GAAG,KAAK,UAAU,QAAQ,iBAAiB,KAAK,QAAQ,iBAAiB;AAClF,2BAAa;AACb,oBAAM,uCAAuC,KAAK,UAAU,QAAQ;AAAA;AAGtE,iBAAK,iBAAiB;AACtB,mBAAO,MAAM,KAAK,YAAY;AAAA,mBACvB,KAAP;AACA,iBAAK,iBAAiB;AACtB,kBAAM;AAAA;AAAA;AAAA;AAIZ,YAAM,KAAK;AAAA;AAGb,QAAI;AAEJ,QAAI;AAEF,YAAM,KAAK,UAAU,SAAS,qBAAqB;AAEnD,eAAS,MAAM,KAAK,KAAK,QAAQ,QAAQ,MAAM,QAAQ;AAEvD,YAAM,KAAK,UAAU,SAAS,oBAAoB,QAAQ;AAAA,aAEnD,OAAP;AACA,UAAI,iBAAiB;AAAc,cAAM,IAAI,OAAO,8BAA8B;AAClF,YAAM;AAAA;AAGR,UAAM;AAEN,WAAO;AAAA;AAAA,EAQT,kBAAkB,YAAY;AAC5B,SAAK,KAAK,QAAQ;AAClB,UAAM;AAAA;AAAA,QAQF,kBAAkB,YAAY;AAClC,UAAM,KAAK,KAAK,QAAQ;AACxB,UAAM,cAAc,WAAW;AAAA;AAAA,QAU3B,SAAS,QAAQ;AACrB,UAAM,KAAK,UAAU,SAAS,iBAAiB;AAC/C,UAAM,aAAa,MAAM,KAAK,QAAQ,kBAAkB,QAAQ;AAChE,UAAM,KAAK,UAAU,SAAS,gBAAgB,YAAY;AAC1D,WAAO;AAAA;AAAA,QAUH,YAAY,YAAY;AAC5B,UAAM,KAAK,UAAU,SAAS,oBAAoB;AAClD,UAAM,KAAK,QAAQ,kBAAkB,WAAW;AAChD,WAAO,KAAK,UAAU,SAAS,mBAAmB;AAAA;AAAA,EAUpD,UAAU,YAAY;AACpB,QAAI,CAAC,KAAK,QAAQ,kBAAkB,UAAU;AAC5C,aAAO;AAAA;AAGT,WAAO,KAAK,QAAQ,kBAAkB,SAAS;AAAA;AAAA;AAInD,OAAO,UAAU;AACjB,OAAO,QAAQ,oBAAoB;AACnC,OAAO,QAAQ,UAAU;", "names": []}