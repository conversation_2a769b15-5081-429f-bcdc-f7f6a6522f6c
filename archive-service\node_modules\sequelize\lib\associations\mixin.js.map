{"version": 3, "sources": ["../../src/associations/mixin.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst HasOne = require('./has-one');\nconst HasMany = require('./has-many');\nconst BelongsToMany = require('./belongs-to-many');\nconst BelongsTo = require('./belongs-to');\n\nfunction isModel(model, sequelize) {\n  return model\n    && model.prototype\n    && model.prototype instanceof sequelize.Sequelize.Model;\n}\n\nconst Mixin = {\n  hasMany(target, options = {}) {\n    if (!isModel(target, this.sequelize)) {\n      throw new Error(`${this.name}.hasMany called with something that's not a subclass of Sequelize.Model`);\n    }\n\n    const source = this;\n\n    // Since this is a mixin, we'll need a unique letiable name for hooks (since Model will override our hooks option)\n    options.hooks = options.hooks === undefined ? false : Bo<PERSON>an(options.hooks);\n    options.useHooks = options.hooks;\n\n    Object.assign(options, _.omit(source.options, ['hooks']));\n\n    if (options.useHooks) {\n      this.runHooks('beforeAssociate', { source, target, type: HasMany }, options);\n    }\n\n    // the id is in the foreign table or in a connecting table\n    const association = new HasMany(source, target, options);\n    source.associations[association.associationAccessor] = association;\n\n    association._injectAttributes();\n    association.mixin(source.prototype);\n\n    if (options.useHooks) {\n      this.runHooks('afterAssociate', { source, target, type: HasMany, association }, options);\n    }\n\n    return association;\n  },\n\n  belongsToMany(target, options = {}) {\n    if (!isModel(target, this.sequelize)) {\n      throw new Error(`${this.name}.belongsToMany called with something that's not a subclass of Sequelize.Model`);\n    }\n\n    const source = this;\n\n    // Since this is a mixin, we'll need a unique letiable name for hooks (since Model will override our hooks option)\n    options.hooks = options.hooks === undefined ? false : Boolean(options.hooks);\n    options.useHooks = options.hooks;\n    options.timestamps = options.timestamps === undefined ? this.sequelize.options.timestamps : options.timestamps;\n    Object.assign(options, _.omit(source.options, ['hooks', 'timestamps', 'scopes', 'defaultScope']));\n\n    if (options.useHooks) {\n      this.runHooks('beforeAssociate', { source, target, type: BelongsToMany }, options);\n    }\n    // the id is in the foreign table or in a connecting table\n    const association = new BelongsToMany(source, target, options);\n    source.associations[association.associationAccessor] = association;\n\n    association._injectAttributes();\n    association.mixin(source.prototype);\n\n    if (options.useHooks) {\n      this.runHooks('afterAssociate', { source, target, type: BelongsToMany, association }, options);\n    }\n\n    return association;\n  },\n\n  getAssociations(target) {\n    return Object.values(this.associations).filter(association => association.target.name === target.name);\n  },\n\n  getAssociationForAlias(target, alias) {\n    // Two associations cannot have the same alias, so we can use find instead of filter\n    return this.getAssociations(target).find(association => association.verifyAssociationAlias(alias)) || null;\n  }\n};\n\n// The logic for hasOne and belongsTo is exactly the same\nfunction singleLinked(Type) {\n  return function(target, options = {}) {\n    // eslint-disable-next-line no-invalid-this\n    const source = this;\n    if (!isModel(target, source.sequelize)) {\n      throw new Error(`${source.name}.${_.lowerFirst(Type.name)} called with something that's not a subclass of Sequelize.Model`);\n    }\n\n\n    // Since this is a mixin, we'll need a unique letiable name for hooks (since Model will override our hooks option)\n    options.hooks = options.hooks === undefined ? false : Boolean(options.hooks);\n    options.useHooks = options.hooks;\n\n    if (options.useHooks) {\n      source.runHooks('beforeAssociate', { source, target, type: Type }, options);\n    }\n    // the id is in the foreign table\n    const association = new Type(source, target, Object.assign(options, source.options));\n    source.associations[association.associationAccessor] = association;\n\n    association._injectAttributes();\n    association.mixin(source.prototype);\n\n    if (options.useHooks) {\n      source.runHooks('afterAssociate', { source, target, type: Type, association }, options);\n    }\n\n    return association;\n  };\n}\n\nMixin.hasOne = singleLinked(HasOne);\nMixin.belongsTo = singleLinked(BelongsTo);\n\nmodule.exports = Mixin;\nmodule.exports.Mixin = Mixin;\nmodule.exports.default = Mixin;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,SAAS,QAAQ;AACvB,MAAM,UAAU,QAAQ;AACxB,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,YAAY,QAAQ;AAE1B,iBAAiB,OAAO,WAAW;AACjC,SAAO,SACF,MAAM,aACN,MAAM,qBAAqB,UAAU,UAAU;AAAA;AAGtD,MAAM,QAAQ;AAAA,EACZ,QAAQ,QAAQ,UAAU,IAAI;AAC5B,QAAI,CAAC,QAAQ,QAAQ,KAAK,YAAY;AACpC,YAAM,IAAI,MAAM,GAAG,KAAK;AAAA;AAG1B,UAAM,SAAS;AAGf,YAAQ,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ,QAAQ;AACtE,YAAQ,WAAW,QAAQ;AAE3B,WAAO,OAAO,SAAS,EAAE,KAAK,OAAO,SAAS,CAAC;AAE/C,QAAI,QAAQ,UAAU;AACpB,WAAK,SAAS,mBAAmB,EAAE,QAAQ,QAAQ,MAAM,WAAW;AAAA;AAItE,UAAM,cAAc,IAAI,QAAQ,QAAQ,QAAQ;AAChD,WAAO,aAAa,YAAY,uBAAuB;AAEvD,gBAAY;AACZ,gBAAY,MAAM,OAAO;AAEzB,QAAI,QAAQ,UAAU;AACpB,WAAK,SAAS,kBAAkB,EAAE,QAAQ,QAAQ,MAAM,SAAS,eAAe;AAAA;AAGlF,WAAO;AAAA;AAAA,EAGT,cAAc,QAAQ,UAAU,IAAI;AAClC,QAAI,CAAC,QAAQ,QAAQ,KAAK,YAAY;AACpC,YAAM,IAAI,MAAM,GAAG,KAAK;AAAA;AAG1B,UAAM,SAAS;AAGf,YAAQ,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ,QAAQ;AACtE,YAAQ,WAAW,QAAQ;AAC3B,YAAQ,aAAa,QAAQ,eAAe,SAAY,KAAK,UAAU,QAAQ,aAAa,QAAQ;AACpG,WAAO,OAAO,SAAS,EAAE,KAAK,OAAO,SAAS,CAAC,SAAS,cAAc,UAAU;AAEhF,QAAI,QAAQ,UAAU;AACpB,WAAK,SAAS,mBAAmB,EAAE,QAAQ,QAAQ,MAAM,iBAAiB;AAAA;AAG5E,UAAM,cAAc,IAAI,cAAc,QAAQ,QAAQ;AACtD,WAAO,aAAa,YAAY,uBAAuB;AAEvD,gBAAY;AACZ,gBAAY,MAAM,OAAO;AAEzB,QAAI,QAAQ,UAAU;AACpB,WAAK,SAAS,kBAAkB,EAAE,QAAQ,QAAQ,MAAM,eAAe,eAAe;AAAA;AAGxF,WAAO;AAAA;AAAA,EAGT,gBAAgB,QAAQ;AACtB,WAAO,OAAO,OAAO,KAAK,cAAc,OAAO,iBAAe,YAAY,OAAO,SAAS,OAAO;AAAA;AAAA,EAGnG,uBAAuB,QAAQ,OAAO;AAEpC,WAAO,KAAK,gBAAgB,QAAQ,KAAK,iBAAe,YAAY,uBAAuB,WAAW;AAAA;AAAA;AAK1G,sBAAsB,MAAM;AAC1B,SAAO,SAAS,QAAQ,UAAU,IAAI;AAEpC,UAAM,SAAS;AACf,QAAI,CAAC,QAAQ,QAAQ,OAAO,YAAY;AACtC,YAAM,IAAI,MAAM,GAAG,OAAO,QAAQ,EAAE,WAAW,KAAK;AAAA;AAKtD,YAAQ,QAAQ,QAAQ,UAAU,SAAY,QAAQ,QAAQ,QAAQ;AACtE,YAAQ,WAAW,QAAQ;AAE3B,QAAI,QAAQ,UAAU;AACpB,aAAO,SAAS,mBAAmB,EAAE,QAAQ,QAAQ,MAAM,QAAQ;AAAA;AAGrE,UAAM,cAAc,IAAI,KAAK,QAAQ,QAAQ,OAAO,OAAO,SAAS,OAAO;AAC3E,WAAO,aAAa,YAAY,uBAAuB;AAEvD,gBAAY;AACZ,gBAAY,MAAM,OAAO;AAEzB,QAAI,QAAQ,UAAU;AACpB,aAAO,SAAS,kBAAkB,EAAE,QAAQ,QAAQ,MAAM,MAAM,eAAe;AAAA;AAGjF,WAAO;AAAA;AAAA;AAIX,MAAM,SAAS,aAAa;AAC5B,MAAM,YAAY,aAAa;AAE/B,OAAO,UAAU;AACjB,OAAO,QAAQ,QAAQ;AACvB,OAAO,QAAQ,UAAU;", "names": []}