{"version": 3, "sources": ["../../../src/dialects/mssql/connection-manager.js"], "sourcesContent": ["'use strict';\n\nconst AbstractConnectionManager = require('../abstract/connection-manager');\nconst AsyncQueue = require('./async-queue').default;\nconst { logger } = require('../../utils/logger');\nconst sequelizeErrors = require('../../errors');\nconst DataTypes = require('../../data-types').mssql;\nconst parserStore = require('../parserStore')('mssql');\nconst debug = logger.debugContext('connection:mssql');\nconst debugTedious = logger.debugContext('connection:mssql:tedious');\n\nclass ConnectionManager extends AbstractConnectionManager {\n  constructor(dialect, sequelize) {\n    sequelize.config.port = sequelize.config.port || 1433;\n    super(dialect, sequelize);\n    this.lib = this._loadDialectModule('tedious');\n    this.refreshTypeParser(DataTypes);\n  }\n\n  _refreshTypeParser(dataType) {\n    parserStore.refresh(dataType);\n  }\n\n  _clearTypeParser() {\n    parserStore.clear();\n  }\n\n  async connect(config) {\n    const connectionConfig = {\n      server: config.host,\n      authentication: {\n        type: 'default',\n        options: {\n          userName: config.username || undefined,\n          password: config.password || undefined\n        }\n      },\n      options: {\n        port: parseInt(config.port, 10),\n        database: config.database,\n        trustServerCertificate: true\n      }\n    };\n\n    if (config.dialectOptions) {\n      // only set port if no instance name was provided\n      if (\n        config.dialectOptions.options &&\n        config.dialectOptions.options.instanceName\n      ) {\n        delete connectionConfig.options.port;\n      }\n\n      if (config.dialectOptions.authentication) {\n        Object.assign(connectionConfig.authentication, config.dialectOptions.authentication);\n      }\n\n      Object.assign(connectionConfig.options, config.dialectOptions.options);\n    }\n\n    try {\n      return await new Promise((resolve, reject) => {\n        const connection = new this.lib.Connection(connectionConfig);\n        if (connection.state === connection.STATE.INITIALIZED) {\n          connection.connect();\n        }\n        connection.queue = new AsyncQueue();\n        connection.lib = this.lib;\n\n        const connectHandler = error => {\n          connection.removeListener('end', endHandler);\n          connection.removeListener('error', errorHandler);\n\n          if (error) return reject(error);\n\n          debug('connection acquired');\n          resolve(connection);\n        };\n\n        const endHandler = () => {\n          connection.removeListener('connect', connectHandler);\n          connection.removeListener('error', errorHandler);\n          reject(new Error('Connection was closed by remote server'));\n        };\n\n        const errorHandler = error => {\n          connection.removeListener('connect', connectHandler);\n          connection.removeListener('end', endHandler);\n          reject(error);\n        };\n\n        connection.once('error', errorHandler);\n        connection.once('end', endHandler);\n        connection.once('connect', connectHandler);\n\n        /*\n         * Permanently attach this event before connection is even acquired\n         * tedious sometime emits error even after connect(with error).\n         *\n         * If we dont attach this even that unexpected error event will crash node process\n         *\n         * E.g. connectTimeout is set higher than requestTimeout\n         */\n        connection.on('error', error => {\n          switch (error.code) {\n            case 'ESOCKET':\n            case 'ECONNRESET':\n              this.pool.destroy(connection);\n          }\n        });\n\n        if (config.dialectOptions && config.dialectOptions.debug) {\n          connection.on('debug', debugTedious.log.bind(debugTedious));\n        }\n      });\n    } catch (error) {\n      if (!error.code) {\n        throw new sequelizeErrors.ConnectionError(error);\n      }\n\n      switch (error.code) {\n        case 'ESOCKET':\n          if (error.message.includes('connect EHOSTUNREACH')) {\n            throw new sequelizeErrors.HostNotReachableError(error);\n          }\n          if (error.message.includes('connect ENETUNREACH')) {\n            throw new sequelizeErrors.HostNotReachableError(error);\n          }\n          if (error.message.includes('connect EADDRNOTAVAIL')) {\n            throw new sequelizeErrors.HostNotReachableError(error);\n          }\n          if (error.message.includes('connect EAFNOSUPPORT')) {\n            throw new sequelizeErrors.HostNotReachableError(error);\n          }\n          if (error.message.includes('getaddrinfo ENOTFOUND')) {\n            throw new sequelizeErrors.HostNotFoundError(error);\n          }\n          if (error.message.includes('connect ECONNREFUSED')) {\n            throw new sequelizeErrors.ConnectionRefusedError(error);\n          }\n          throw new sequelizeErrors.ConnectionError(error);\n        case 'ER_ACCESS_DENIED_ERROR':\n        case 'ELOGIN':\n          throw new sequelizeErrors.AccessDeniedError(error);\n        case 'EINVAL':\n          throw new sequelizeErrors.InvalidConnectionError(error);\n        default:\n          throw new sequelizeErrors.ConnectionError(error);\n      }\n    }\n  }\n\n  async disconnect(connection) {\n    // Don't disconnect a connection that is already disconnected\n    if (connection.closed) {\n      return;\n    }\n\n    connection.queue.close();\n\n    return new Promise(resolve => {\n      connection.on('end', resolve);\n      connection.close();\n      debug('connection closed');\n    });\n  }\n\n  validate(connection) {\n    return connection && (connection.loggedIn || connection.state.name === 'LoggedIn');\n  }\n}\n\nmodule.exports = ConnectionManager;\nmodule.exports.ConnectionManager = ConnectionManager;\nmodule.exports.default = ConnectionManager;\n"], "mappings": ";AAEA,MAAM,4BAA4B,QAAQ;AAC1C,MAAM,aAAa,QAAQ,iBAAiB;AAC5C,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,kBAAkB,QAAQ;AAChC,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,QAAQ,OAAO,aAAa;AAClC,MAAM,eAAe,OAAO,aAAa;AAEzC,gCAAgC,0BAA0B;AAAA,EACxD,YAAY,SAAS,WAAW;AAC9B,cAAU,OAAO,OAAO,UAAU,OAAO,QAAQ;AACjD,UAAM,SAAS;AACf,SAAK,MAAM,KAAK,mBAAmB;AACnC,SAAK,kBAAkB;AAAA;AAAA,EAGzB,mBAAmB,UAAU;AAC3B,gBAAY,QAAQ;AAAA;AAAA,EAGtB,mBAAmB;AACjB,gBAAY;AAAA;AAAA,QAGR,QAAQ,QAAQ;AACpB,UAAM,mBAAmB;AAAA,MACvB,QAAQ,OAAO;AAAA,MACf,gBAAgB;AAAA,QACd,MAAM;AAAA,QACN,SAAS;AAAA,UACP,UAAU,OAAO,YAAY;AAAA,UAC7B,UAAU,OAAO,YAAY;AAAA;AAAA;AAAA,MAGjC,SAAS;AAAA,QACP,MAAM,SAAS,OAAO,MAAM;AAAA,QAC5B,UAAU,OAAO;AAAA,QACjB,wBAAwB;AAAA;AAAA;AAI5B,QAAI,OAAO,gBAAgB;AAEzB,UACE,OAAO,eAAe,WACtB,OAAO,eAAe,QAAQ,cAC9B;AACA,eAAO,iBAAiB,QAAQ;AAAA;AAGlC,UAAI,OAAO,eAAe,gBAAgB;AACxC,eAAO,OAAO,iBAAiB,gBAAgB,OAAO,eAAe;AAAA;AAGvE,aAAO,OAAO,iBAAiB,SAAS,OAAO,eAAe;AAAA;AAGhE,QAAI;AACF,aAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC5C,cAAM,aAAa,IAAI,KAAK,IAAI,WAAW;AAC3C,YAAI,WAAW,UAAU,WAAW,MAAM,aAAa;AACrD,qBAAW;AAAA;AAEb,mBAAW,QAAQ,IAAI;AACvB,mBAAW,MAAM,KAAK;AAEtB,cAAM,iBAAiB,WAAS;AAC9B,qBAAW,eAAe,OAAO;AACjC,qBAAW,eAAe,SAAS;AAEnC,cAAI;AAAO,mBAAO,OAAO;AAEzB,gBAAM;AACN,kBAAQ;AAAA;AAGV,cAAM,aAAa,MAAM;AACvB,qBAAW,eAAe,WAAW;AACrC,qBAAW,eAAe,SAAS;AACnC,iBAAO,IAAI,MAAM;AAAA;AAGnB,cAAM,eAAe,WAAS;AAC5B,qBAAW,eAAe,WAAW;AACrC,qBAAW,eAAe,OAAO;AACjC,iBAAO;AAAA;AAGT,mBAAW,KAAK,SAAS;AACzB,mBAAW,KAAK,OAAO;AACvB,mBAAW,KAAK,WAAW;AAU3B,mBAAW,GAAG,SAAS,WAAS;AAC9B,kBAAQ,MAAM;AAAA,iBACP;AAAA,iBACA;AACH,mBAAK,KAAK,QAAQ;AAAA;AAAA;AAIxB,YAAI,OAAO,kBAAkB,OAAO,eAAe,OAAO;AACxD,qBAAW,GAAG,SAAS,aAAa,IAAI,KAAK;AAAA;AAAA;AAAA,aAG1C,OAAP;AACA,UAAI,CAAC,MAAM,MAAM;AACf,cAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAG5C,cAAQ,MAAM;AAAA,aACP;AACH,cAAI,MAAM,QAAQ,SAAS,yBAAyB;AAClD,kBAAM,IAAI,gBAAgB,sBAAsB;AAAA;AAElD,cAAI,MAAM,QAAQ,SAAS,wBAAwB;AACjD,kBAAM,IAAI,gBAAgB,sBAAsB;AAAA;AAElD,cAAI,MAAM,QAAQ,SAAS,0BAA0B;AACnD,kBAAM,IAAI,gBAAgB,sBAAsB;AAAA;AAElD,cAAI,MAAM,QAAQ,SAAS,yBAAyB;AAClD,kBAAM,IAAI,gBAAgB,sBAAsB;AAAA;AAElD,cAAI,MAAM,QAAQ,SAAS,0BAA0B;AACnD,kBAAM,IAAI,gBAAgB,kBAAkB;AAAA;AAE9C,cAAI,MAAM,QAAQ,SAAS,yBAAyB;AAClD,kBAAM,IAAI,gBAAgB,uBAAuB;AAAA;AAEnD,gBAAM,IAAI,gBAAgB,gBAAgB;AAAA,aACvC;AAAA,aACA;AACH,gBAAM,IAAI,gBAAgB,kBAAkB;AAAA,aACzC;AACH,gBAAM,IAAI,gBAAgB,uBAAuB;AAAA;AAEjD,gBAAM,IAAI,gBAAgB,gBAAgB;AAAA;AAAA;AAAA;AAAA,QAK5C,WAAW,YAAY;AAE3B,QAAI,WAAW,QAAQ;AACrB;AAAA;AAGF,eAAW,MAAM;AAEjB,WAAO,IAAI,QAAQ,aAAW;AAC5B,iBAAW,GAAG,OAAO;AACrB,iBAAW;AACX,YAAM;AAAA;AAAA;AAAA,EAIV,SAAS,YAAY;AACnB,WAAO,cAAe,YAAW,YAAY,WAAW,MAAM,SAAS;AAAA;AAAA;AAI3E,OAAO,UAAU;AACjB,OAAO,QAAQ,oBAAoB;AACnC,OAAO,QAAQ,UAAU;", "names": []}