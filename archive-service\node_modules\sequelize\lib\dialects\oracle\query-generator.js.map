{"version": 3, "sources": ["../../../src/dialects/oracle/query-generator.js"], "sourcesContent": ["// Copyright (c) 2022, Oracle and/or its affiliates. All rights reserved\n\n'use strict';\n\nconst Utils = require('../../utils');\nconst DataTypes = require('../../data-types');\nconst AbstractQueryGenerator = require('../abstract/query-generator');\nconst _ = require('lodash');\nconst util = require('util');\nconst Transaction = require('../../transaction');\n\n/**\n * list of reserved words in Oracle DB 21c\n * source: https://www.oracle.com/pls/topic/lookup?ctx=dblatest&id=GUID-7B72E154-677A-4342-A1EA-C74C1EA928E6\n *\n * @private\n */\nconst ORACLE_RESERVED_WORDS = ['ACCESS', 'ADD', 'ALL', 'ALTER', 'AND', 'ANY', 'ARRAYLEN', 'AS', 'ASC', 'AUDIT', 'BETWEEN', 'BY', 'CHAR', 'CHECK', 'CLUSTER', 'COLUMN', 'COMMENT', 'COMPRESS', 'CONNECT', 'CREATE', 'CURRENT', 'DATE', 'DECIMAL', 'DEFAULT', 'DELETE', 'DESC', 'DISTINCT', 'DROP', 'ELSE', 'EXCLUSIVE', 'EXISTS', 'FILE', 'FLOAT', 'FOR', 'FROM', 'GRANT', 'GROUP', 'HAVING', 'IDENTIFIED', 'IMMEDIATE', 'IN', 'INCREMENT', 'INDEX', 'INITIAL', 'INSERT', 'INTEGER', 'INTERSECT', 'INTO', 'IS', 'LEVEL', 'LIKE', 'LOCK', 'LONG', 'MAXEXTENTS', 'MINUS', 'MODE', 'MODIFY', 'NOAUDIT', 'NOCOMPRESS', 'NOT', 'NOTFOUND', 'NOWAIT', 'NULL', 'NUMBER', 'OF', 'OFFLINE', 'ON', 'ONLINE', 'OPTION', 'OR', 'ORDER', 'PCTFREE', 'PRIOR', 'PRIVILEGES', 'PUBLIC', 'RAW', 'RENAME', 'RESOURCE', 'REVOKE', 'ROW', 'ROWID', 'ROWLABEL', 'ROWNUM', 'ROWS', 'SELECT', 'SESSION', 'SET', 'SHARE', 'SIZE', 'SMALLINT', 'SQLBUF', 'START', 'SUCCESSFUL', 'SYNONYM', 'SYSDATE', 'TABLE', 'THEN', 'TO', 'TRIGGER', 'UID', 'UNION', 'UNIQUE', 'UPDATE', 'USER', 'VALIDATE', 'VALUES', 'VARCHAR', 'VARCHAR2', 'VIEW', 'WHENEVER', 'WHERE', 'WITH'];\nconst JSON_FUNCTION_REGEX = /^\\s*((?:[a-z]+_){0,2}jsonb?(?:_[a-z]+){0,2})\\([^)]*\\)/i;\nconst JSON_OPERATOR_REGEX = /^\\s*(->>?|@>|<@|\\?[|&]?|\\|{2}|#-)/i;\nconst TOKEN_CAPTURE_REGEX = /^\\s*((?:([`\"'])(?:(?!\\2).|\\2{2})*\\2)|[\\w\\d\\s]+|[().,;+-])/i;\n\nexport class OracleQueryGenerator extends AbstractQueryGenerator {\n  constructor(options) {\n    super(options);\n  }\n\n  /**\n   * Returns the value as it is stored in the Oracle DB\n   *\n   * @param {string} value\n   */\n  getCatalogName(value) {\n    if (value) {\n      if (this.options.quoteIdentifiers === false) {\n        const quotedValue = this.quoteIdentifier(value);\n        if (quotedValue === value) {\n          value = value.toUpperCase();\n        }\n      }\n    }\n    return value;\n  }\n\n  /**\n   * Returns the tableName and schemaName as it is stored the Oracle DB\n   *\n   * @param {object|string} table\n   */\n  getSchemaNameAndTableName(table) {\n    const tableName = this.getCatalogName(table.tableName || table);\n    const schemaName = this.getCatalogName(table.schema);\n    return [tableName, schemaName];\n  }\n\n  createSchema(schema) {\n    const quotedSchema = this.quoteIdentifier(schema);\n    return [\n      'DECLARE',\n      'USER_FOUND BOOLEAN := FALSE;',\n      'BEGIN',\n      ' BEGIN',\n      '   EXECUTE IMMEDIATE ',\n      this.escape(`CREATE USER ${quotedSchema} IDENTIFIED BY 12345 DEFAULT TABLESPACE USERS`),\n      ';',\n      '   EXCEPTION WHEN OTHERS THEN',\n      '     IF SQLCODE != -1920 THEN',\n      '       RAISE;',\n      '     ELSE',\n      '       USER_FOUND := TRUE;',\n      '     END IF;',\n      ' END;',\n      ' IF NOT USER_FOUND THEN',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`GRANT \"CONNECT\" TO ${quotedSchema}`),\n      ';',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`GRANT CREATE TABLE TO ${quotedSchema}`),\n      ';',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`GRANT CREATE VIEW TO ${quotedSchema}`),\n      ';',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`GRANT CREATE ANY TRIGGER TO ${quotedSchema}`),\n      ';',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`GRANT CREATE ANY PROCEDURE TO ${quotedSchema}`),\n      ';',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`GRANT CREATE SEQUENCE TO ${quotedSchema}`),\n      ';',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`GRANT CREATE SYNONYM TO ${quotedSchema}`),\n      ';',\n      '    EXECUTE IMMEDIATE ',\n      this.escape(`ALTER USER ${quotedSchema} QUOTA UNLIMITED ON USERS`),\n      ';',\n      ' END IF;',\n      'END;'\n    ].join(' ');\n  }\n\n  showSchemasQuery() {\n    return 'SELECT USERNAME AS \"schema_name\" FROM ALL_USERS WHERE COMMON = (\\'NO\\') AND USERNAME != user';\n  }\n\n  dropSchema(schema) {\n    return [\n      'BEGIN',\n      'EXECUTE IMMEDIATE ',\n      this.escape(`DROP USER ${this.quoteTable(schema)} CASCADE`),\n      ';',\n      'EXCEPTION WHEN OTHERS THEN',\n      '  IF SQLCODE != -1918 THEN',\n      '    RAISE;',\n      '  END IF;',\n      'END;'\n    ].join(' ');\n  }\n\n  versionQuery() {\n    return \"SELECT VERSION_FULL FROM PRODUCT_COMPONENT_VERSION WHERE PRODUCT LIKE 'Oracle%'\";\n  }\n\n  createTableQuery(tableName, attributes, options) {\n    const primaryKeys = [],\n      foreignKeys = Object.create(null),\n      attrStr = [],\n      checkStr = [];\n\n    const values = {\n      table: this.quoteTable(tableName)\n    };\n\n    // Starting by dealing with all attributes\n    for (let attr in attributes) {\n      if (!Object.prototype.hasOwnProperty.call(attributes, attr)) continue;\n      const dataType = attributes[attr];\n      attr = this.quoteIdentifier(attr);\n\n      // ORACLE doesn't support inline REFERENCES declarations: move to the end\n      if (dataType.includes('PRIMARY KEY')) {\n        // Primary key\n        primaryKeys.push(attr);\n        if (dataType.includes('REFERENCES')) {\n          const match = dataType.match(/^(.+) (REFERENCES.*)$/);\n          attrStr.push(`${attr} ${match[1].replace(/PRIMARY KEY/, '')}`);\n\n          // match[2] already has foreignKeys in correct format so we don't need to replace\n          foreignKeys[attr] = match[2];\n        } else {\n          attrStr.push(`${attr} ${dataType.replace(/PRIMARY KEY/, '').trim()}`);\n        }\n      } else if (dataType.includes('REFERENCES')) {\n        // Foreign key\n        const match = dataType.match(/^(.+) (REFERENCES.*)$/);\n        attrStr.push(`${attr} ${match[1]}`);\n\n        // match[2] already has foreignKeys in correct format so we don't need to replace\n        foreignKeys[attr] = match[2];\n      } else {\n        attrStr.push(`${attr} ${dataType}`);\n      }\n    }\n\n    values['attributes'] = attrStr.join(', ');\n\n    const pkString = primaryKeys.map(pk => this.quoteIdentifier(pk)).join(', ');\n\n    if (pkString.length > 0) {\n      values.attributes += `,PRIMARY KEY (${pkString})`;\n    }\n\n    // Dealing with FKs\n    for (const fkey in foreignKeys) {\n      if (!Object.prototype.hasOwnProperty.call(foreignKeys, fkey)) continue; \n      // Oracle default response for FK, doesn't support if defined\n      if (foreignKeys[fkey].indexOf('ON DELETE NO ACTION') > -1) {\n        foreignKeys[fkey] = foreignKeys[fkey].replace('ON DELETE NO ACTION', '');\n      }\n      values.attributes += `,FOREIGN KEY (${this.quoteIdentifier(fkey)}) ${foreignKeys[fkey]}`;\n    }\n\n    if (checkStr.length > 0) {\n      values.attributes += `, ${checkStr.join(', ')}`;\n    }\n\n    // Specific case for unique indexes with Oracle, we have to set the constraint on the column, if not, no FK will be possible (ORA-02270: no matching unique or primary key for this column-list)\n    if (options && options.indexes && options.indexes.length > 0) {\n      const idxToDelete = [];\n      options.indexes.forEach((index, idx) => {\n        if ('unique' in index && (index.unique === true || index.unique.length > 0 && index.unique !== false)) {\n          // If unique index, transform to unique constraint on column\n          const fields = index.fields.map(field => {\n            if (typeof field === 'string') {\n              return field;\n            } \n            return field.attribute;\n            \n          });\n\n          // Now we have to be sure that the constraint isn't already declared in uniqueKeys\n          let canContinue = true;\n          if (options.uniqueKeys) {\n            const keys = Object.keys(options.uniqueKeys);\n\n            for (let fieldIdx = 0; fieldIdx < keys.length; fieldIdx++) {\n              const currUnique = options.uniqueKeys[keys[fieldIdx]];\n\n              if (currUnique.fields.length === fields.length) {\n                // lengths are the same, possible same constraint\n                for (let i = 0; i < currUnique.fields.length; i++) {\n                  const field = currUnique.fields[i];\n\n                  if (_.includes(fields, field)) {\n                    canContinue = false;\n                  } else {\n                    // We have at least one different column, even if we found the same columns previously, we let the constraint be created\n                    canContinue = true;\n                    break;\n                  }\n                }\n              }\n            }\n\n            if (canContinue) {\n              const indexName = 'name' in index ? index.name : '';\n              const constraintToAdd = {\n                name: indexName,\n                fields\n              };\n              if (!('uniqueKeys' in options)) {\n                options.uniqueKeys = {};\n              }\n\n              options.uniqueKeys[indexName] = constraintToAdd;\n              idxToDelete.push(idx);\n            } else {\n              // The constraint already exists, we remove it from the list\n              idxToDelete.push(idx);\n            }\n          }\n        }\n      });\n      idxToDelete.forEach(idx => {\n        options.indexes.splice(idx, 1);\n      });\n    }\n\n    if (options && !!options.uniqueKeys) {\n      _.each(options.uniqueKeys, (columns, indexName) => {\n        let canBeUniq = false;\n\n        // Check if we can create the unique key\n        primaryKeys.forEach(primaryKey => {\n          // We can create an unique constraint if it's not on the primary key AND if it doesn't have unique in its definition\n          // We replace quotes in primary key with ''\n          // Primary key would be a list with double quotes in it so we remove the double quotes\n          primaryKey = primaryKey.replace(/\"/g, '');\n\n          // We check if the unique indexes are already a part of primary key or not\n          // If it is not then we set canbeuniq to true and add a unique constraint to these fields.\n          // Else we can ignore unique constraint on these\n          if (!_.includes(columns.fields, primaryKey)) {\n            canBeUniq = true;\n          }\n        });\n\n        columns.fields.forEach(field => {\n          let currField = '';\n          if (!_.isString(field)) {\n            currField = field.attribute.replace(/[.,\"\\s]/g, '');\n          } else {\n            currField = field.replace(/[.,\"\\s]/g, '');\n          }\n          if (currField in attributes) {\n            // If canBeUniq is false we need not replace the UNIQUE for the attribute\n            // So we replace UNIQUE with '' only if there exists a primary key\n            if (attributes[currField].toUpperCase().indexOf('UNIQUE') > -1 && canBeUniq) {\n              // We generate the attribute without UNIQUE\n              const attrToReplace = attributes[currField].replace('UNIQUE', '');\n              // We replace in the final string\n              values.attributes = values.attributes.replace(attributes[currField], attrToReplace);\n            }\n          }\n        });\n\n        // Oracle cannot have an unique AND a primary key on the same fields, prior to the primary key\n        if (canBeUniq) {\n          const index = options.uniqueKeys[columns.name];\n          delete options.uniqueKeys[columns.name];\n          indexName = indexName.replace(/[.,\\s]/g, '');\n          columns.name = indexName;\n          options.uniqueKeys[indexName] = index;\n\n          // Autogenerate Constraint name, if no indexName is given\n          if (indexName.length === 0) {\n            values.attributes += `,UNIQUE (${columns.fields.map(field => this.quoteIdentifier(field)).join(', ') })`;\n          } else {\n            values.attributes +=\n              `, CONSTRAINT ${this.quoteIdentifier(indexName)} UNIQUE (${columns.fields.map(field => this.quoteIdentifier(field)).join(', ') })`;\n          }\n        }\n      });\n    }\n\n    // we replace single quotes by two quotes in order for the execute statement to work\n    const query = Utils.joinSQLFragments([\n      'CREATE TABLE',\n      values.table,\n      `(${values.attributes})`\n    ]);\n\n    return Utils.joinSQLFragments([\n      'BEGIN',\n      'EXECUTE IMMEDIATE',\n      `${this.escape(query)};`,\n      'EXCEPTION WHEN OTHERS THEN',\n      'IF SQLCODE != -955 THEN',\n      'RAISE;',\n      'END IF;',\n      'END;'\n    ]);\n  }\n\n  tableExistsQuery(table) {\n    const [tableName, schemaName] = this.getSchemaNameAndTableName(table);\n    return `SELECT TABLE_NAME FROM ALL_TABLES WHERE TABLE_NAME = ${this.escape(tableName)} AND OWNER = ${table.schema ? this.escape(schemaName) : 'USER'}`;\n  }\n  \n  describeTableQuery(tableName, schema) {\n    const currTableName = this.getCatalogName(tableName.tableName || tableName);\n    schema = this.getCatalogName(schema);\n    // name, type, datalength (except number / nvarchar), datalength varchar, datalength number, nullable, default value, primary ?\n    return [\n      'SELECT atc.COLUMN_NAME, atc.DATA_TYPE, atc.DATA_LENGTH, atc.CHAR_LENGTH, atc.DEFAULT_LENGTH, atc.NULLABLE, ucc.constraint_type ',\n      'FROM all_tab_columns atc ',\n      'LEFT OUTER JOIN ',\n      '(SELECT acc.column_name, acc.table_name, ac.constraint_type FROM all_cons_columns acc INNER JOIN all_constraints ac ON acc.constraint_name = ac.constraint_name) ucc ',\n      'ON (atc.table_name = ucc.table_name AND atc.COLUMN_NAME = ucc.COLUMN_NAME) ',\n      schema\n        ? `WHERE (atc.OWNER = ${this.escape(schema)}) `\n        : 'WHERE atc.OWNER = USER ',\n      `AND (atc.TABLE_NAME = ${this.escape(currTableName)})`,\n      'ORDER BY atc.COLUMN_NAME, CONSTRAINT_TYPE DESC'\n    ].join('');\n  }\n\n  renameTableQuery(before, after) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(before),\n      'RENAME TO',\n      this.quoteTable(after)\n    ]);\n  }\n\n  showConstraintsQuery(table) {\n    const tableName = this.getCatalogName(table.tableName || table);\n    return `SELECT CONSTRAINT_NAME constraint_name FROM user_cons_columns WHERE table_name = ${this.escape(tableName)}`;\n  }\n\n  showTablesQuery() {\n    return 'SELECT owner as table_schema, table_name, 0 as lvl FROM all_tables where OWNER IN(SELECT USERNAME AS \"schema_name\" FROM ALL_USERS WHERE ORACLE_MAINTAINED = \\'N\\')';\n  }\n\n  dropTableQuery(tableName) {\n    return Utils.joinSQLFragments([\n      'BEGIN ',\n      'EXECUTE IMMEDIATE \\'DROP TABLE',\n      this.quoteTable(tableName),\n      'CASCADE CONSTRAINTS PURGE\\';',\n      'EXCEPTION WHEN OTHERS THEN',\n      ' IF SQLCODE != -942 THEN',\n      '   RAISE;',\n      ' END IF;',\n      'END;'\n    ]);\n  }\n\n  /*\n    Modifying the indexname so that it is prefixed with the schema name\n    otherwise Oracle tries to add the index to the USER schema\n   @overide\n  */\n  addIndexQuery(tableName, attributes, options, rawTablename) {\n    if (typeof tableName !== 'string' && attributes.name) {\n      attributes.name = `${tableName.schema}.${attributes.name}`;\n    }\n    return super.addIndexQuery(tableName, attributes, options, rawTablename);\n  }\n\n  addConstraintQuery(tableName, options) {\n    options = options || {};\n\n    if (options.onUpdate) {\n      // Oracle does not support ON UPDATE, remove it.\n      delete options.onUpdate;\n    }\n\n    if (options.onDelete && options.onDelete.toUpperCase() === 'NO ACTION') {\n      // 'ON DELETE NO ACTION' is the default option in Oracle, but it is not supported if defined\n      delete options.onDelete;\n    }\n\n    const constraintSnippet = this.getConstraintSnippet(tableName, options);\n\n    tableName = this.quoteTable(tableName);\n    return `ALTER TABLE ${tableName} ADD ${constraintSnippet};`;\n  }\n\n  addColumnQuery(table, key, dataType) {\n    dataType.field = key;\n\n    const attribute = Utils.joinSQLFragments([\n      this.quoteIdentifier(key),\n      this.attributeToSQL(dataType, {\n        attributeName: key,\n        context: 'addColumn'\n      })\n    ]);\n\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(table),\n      'ADD',\n      attribute\n    ]);\n  }\n\n  removeColumnQuery(tableName, attributeName) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP COLUMN',\n      this.quoteIdentifier(attributeName),\n      ';'\n    ]);\n  }\n\n  /**\n   * Function to add new foreign key to the attribute \n   * Block for add and drop foreign key constraint query\n   * taking the assumption that there is a single column foreign key reference always\n   * i.e. we always do - FOREIGN KEY (a) reference B(a) during createTable queryGenerator\n   * so there would be one and only one match for a constraint name for each column\n   * and every foreign keyed column would have a different constraint name\n   * Since sequelize doesn't support multiple column foreign key, added complexity to\n   * add the feature isn't needed\n   *\n   * @param {string} definition The operation that needs to be performed on the attribute\n   * @param {string|object} table The table that needs to be altered\n   * @param {string} attributeName The name of the attribute which would get altered\n   */\n  _alterForeignKeyConstraint(definition, table, attributeName) {\n    const [tableName, schemaName] = this.getSchemaNameAndTableName(table);\n    const attributeNameConstant = this.escape(this.getCatalogName(attributeName));\n    const schemaNameConstant = table.schema ? this.escape(this.getCatalogName(schemaName)) : 'USER';\n    const tableNameConstant = this.escape(this.getCatalogName(tableName));\n    const getConsNameQuery = [\n      'SELECT constraint_name INTO cons_name',\n      'FROM (',\n      '  SELECT DISTINCT cc.owner, cc.table_name, cc.constraint_name, cc.column_name AS cons_columns',\n      '  FROM all_cons_columns cc, all_constraints c',\n      '  WHERE cc.owner = c.owner',\n      '  AND cc.table_name = c.table_name',\n      '  AND cc.constraint_name = c.constraint_name',\n      '  AND c.constraint_type = \\'R\\'',\n      '  GROUP BY cc.owner, cc.table_name, cc.constraint_name, cc.column_name',\n      ')',\n      'WHERE owner =',\n      schemaNameConstant,\n      'AND table_name =',\n      tableNameConstant,\n      'AND cons_columns =',\n      attributeNameConstant,\n      ';'\n    ].join(' ');\n    const secondQuery = Utils.joinSQLFragments([\n      `ALTER TABLE ${this.quoteIdentifier(tableName)}`,\n      'ADD FOREIGN KEY',\n      `(${this.quoteIdentifier(attributeName)})`,\n      definition.replace(/.+?(?=REFERENCES)/, '')\n    ]);\n    return [\n      'BEGIN',\n      getConsNameQuery,\n      'EXCEPTION',\n      'WHEN NO_DATA_FOUND THEN',\n      ' CONS_NAME := NULL;',\n      'END;',\n      'IF CONS_NAME IS NOT NULL THEN',\n      ` EXECUTE IMMEDIATE 'ALTER TABLE ${this.quoteTable(table)} DROP CONSTRAINT \"'||CONS_NAME||'\"';`,\n      'END IF;',\n      `EXECUTE IMMEDIATE ${this.escape(secondQuery)};`\n    ].join(' ');\n  }\n\n  /**\n   * Function to alter table modify\n   *\n   * @param {string} definition The operation that needs to be performed on the attribute\n   * @param {object|string} table The table that needs to be altered\n   * @param {string} attributeName The name of the attribute which would get altered\n   */\n  _modifyQuery(definition, table, attributeName) {\n    definition = definition.startsWith('BLOB') ? definition.replace('BLOB ', '') : definition;\n    const query = Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(table),\n      'MODIFY',\n      this.quoteIdentifier(attributeName),\n      definition\n    ]);\n    const secondQuery = query.replace('NOT NULL', '').replace('NULL', '');\n    return [\n      'BEGIN',\n      `EXECUTE IMMEDIATE ${this.escape(query)};`,\n      'EXCEPTION',\n      'WHEN OTHERS THEN',\n      ' IF SQLCODE = -1442 OR SQLCODE = -1451 THEN',\n      // We execute the statement without the NULL / NOT NULL clause if the first statement failed due to this\n      `   EXECUTE IMMEDIATE ${this.escape(secondQuery)};`,\n      ' ELSE',\n      '   RAISE;',\n      ' END IF;',\n      'END;'\n    ].join(' ');\n  }\n\n  changeColumnQuery(table, attributes) {\n    const sql = [\n      'DECLARE',\n      'CONS_NAME VARCHAR2(200);',\n      'BEGIN'\n    ];\n    for (const attributeName in attributes) {\n      if (!Object.prototype.hasOwnProperty.call(attributes, attributeName)) continue;\n      const definition = attributes[attributeName];\n      if (definition.match(/REFERENCES/)) {\n        sql.push(this._alterForeignKeyConstraint(definition, table, attributeName));\n      } else {\n        // Building the modify query\n        sql.push(this._modifyQuery(definition, table, attributeName));\n      }\n    }\n    sql.push('END;');\n    return sql.join(' ');\n  }\n\n  renameColumnQuery(tableName, attrBefore, attributes) {\n    const newName = Object.keys(attributes)[0];\n    return `ALTER TABLE ${this.quoteTable(tableName)} RENAME COLUMN ${this.quoteIdentifier(attrBefore)} TO ${this.quoteIdentifier(newName)}`;\n  }\n\n  /**\n   * Populates the returnAttributes array with outbind bindByPosition values\n   * and also the options.outBindAttributes map with bindDef for outbind of InsertQuery\n   *\n   * @param {Array} returningModelAttributes\n   * @param {Array} returnTypes\n   * @param {number} inbindLength\n   * @param {object} returnAttributes\n   * @param {object} options\n   *\n   * @private\n   */\n  populateInsertQueryReturnIntoBinds(returningModelAttributes, returnTypes, inbindLength, returnAttributes, options) {\n    const oracledb = this.sequelize.connectionManager.lib;\n    const outBindAttributes = Object.create(null);\n    const outbind = [];\n    const outbindParam = this.bindParam(outbind, inbindLength);\n    returningModelAttributes.forEach((element, index) => {\n      // generateReturnValues function quotes identifier based on the quoteIdentifier option\n      // If the identifier starts with a quote we remove it else we use it as is\n      if (element.startsWith('\"')) {\n        element = element.substring(1, element.length - 1);\n      }\n      outBindAttributes[element] = Object.assign(returnTypes[index]._getBindDef(oracledb), { dir: oracledb.BIND_OUT });\n      const returnAttribute = `${this.format(undefined, undefined, { context: 'INSERT' }, outbindParam)}`;\n      returnAttributes.push(returnAttribute);\n    });\n    options.outBindAttributes = outBindAttributes;\n  }\n\n  /**\n   * Override of upsertQuery, Oracle specific\n   * Using PL/SQL for finding the row\n   *\n   * @param {object|string} tableName\n   * @param {Array} insertValues\n   * @param {Array} updateValues\n   * @param {Array} where\n   * @param {object} model\n   * @param {object} options\n   */\n  upsertQuery(tableName, insertValues, updateValues, where, model, options) {\n    const rawAttributes = model.rawAttributes;\n    const updateQuery = this.updateQuery(tableName, updateValues, where, options, rawAttributes);\n    // This bind is passed so that the insert query starts appending to this same bind array\n    options.bind = updateQuery.bind;\n    const insertQuery = this.insertQuery(tableName, insertValues, rawAttributes, options);\n\n    const sql = [\n      'DECLARE ',\n      'BEGIN ',\n      updateQuery.query ? [ \n        updateQuery.query,\n        '; ',\n        ' IF ( SQL%ROWCOUNT = 0 ) THEN ',\n        insertQuery.query,\n        ' :isUpdate := 0; ',\n        'ELSE ',\n        ' :isUpdate := 1; ',\n        ' END IF; '\n      ].join('') : [\n        insertQuery.query,\n        ' :isUpdate := 0; ',\n        // If there is a conflict on insert we ignore\n        'EXCEPTION WHEN OTHERS THEN',\n        ' IF SQLCODE != -1 THEN',\n        '   RAISE;',\n        ' END IF;'\n      ].join(''),\n      'END;'\n    ];\n\n    const query = sql.join('');\n    const result = { query };\n    \n    if (options.bindParam !== false) {\n      result.bind = updateQuery.bind || insertQuery.bind;\n    }\n\n    return result;\n  }\n\n  /**\n   * Returns an insert into command for multiple values.\n   *\n   * @param {string} tableName\n   * @param {object} fieldValueHashes\n   * @param {object} options\n   * @param {object} fieldMappedAttributes\n   *\n   * @private\n   */\n  bulkInsertQuery(tableName, fieldValueHashes, options, fieldMappedAttributes) {\n    options = options || {};\n    options.executeMany = true;\n    fieldMappedAttributes = fieldMappedAttributes || {};\n\n    const tuples = [];\n    const allColumns = {};\n    const inBindBindDefMap = {};\n    const outBindBindDefMap = {};\n    const oracledb = this.sequelize.connectionManager.lib;\n\n    // Generating the allColumns map\n    // The data is provided as an array of objects. \n    // Each object may contain differing numbers of attributes. \n    // A set of the attribute names that are used in all objects must be determined. \n    // The allColumns map contains the column names and indicates whether the value is generated or not\n    // We set allColumns[key] to true if the field is an\n    // auto-increment field and the value given is null and fieldMappedAttributes[key]\n    // is valid for the specific column else it is set to false\n    for (const fieldValueHash of fieldValueHashes) {\n      _.forOwn(fieldValueHash, (value, key) => {\n        allColumns[key] = fieldMappedAttributes[key] && fieldMappedAttributes[key].autoIncrement === true && value === null;\n      });\n    }\n\n    // Building the inbind parameter\n    // A list that would have inbind positions like [:1, :2, :3...] to be used in generating sql string\n    let inBindPosition;\n    // Iterating over each row of the fieldValueHashes\n    for (const fieldValueHash of fieldValueHashes) {\n      // Has each column for a row after coverting it to appropriate format using this.format function\n      // like ['Mick', 'Broadstone', 2022-02-16T05:24:18.949Z, 2022-02-16T05:24:18.949Z],\n      const tuple = [];\n      // A function expression for this.bindParam/options.bindparam function\n      // This function is passed to this.format function which inserts column values to the tuple list\n      // using _bindParam/_stringify function in data-type.js file\n      const inbindParam = options.bindParam === undefined ? this.bindParam(tuple) : options.bindParam;\n      // We are iterating over each col\n      // and pushing the given values to tuple list using this.format function\n      // and also simultaneously generating the bindPosition\n      // tempBindPostions has the inbind positions\n      const tempBindPositions = Object.keys(allColumns).map(key => {\n        if (allColumns[key] === true) {\n          // We had set allAttributes[key] to true since at least one row for an auto increment column was null\n          // If we get any other row that has this specific column as non-null we must raise an error\n          // Since for an auto-increment column, either all row has to be null or all row has to be a non-null\n          if (fieldValueHash[key] !== null) {\n            throw Error('For an auto-increment column either all row must be null or non-null, a mix of null and non-null is not allowed!');\n          }\n          // Return DEFAULT for auto-increment column and if all values for the column is null in each row\n          return 'DEFAULT';\n        }\n        // Sanitizes the values given by the user and pushes it to the tuple list using inBindParam function and\n        // also generates the inbind position for the sql string for example (:1, :2, :3.....) which is a by product of the push\n        return this.format(fieldValueHash[key], fieldMappedAttributes[key], { context: 'INSERT' }, inbindParam);\n      });\n\n      // Even though the bind variable positions are calculated for each row we only retain the values for the first row \n      // since the values will be identical\n      if (!inBindPosition) {\n        inBindPosition = tempBindPositions;\n      }\n      // Adding the row to the array of rows that will be supplied to executeMany()\n      tuples.push(tuple);\n    }\n\n    // The columns that we are expecting to be returned from the DB like [\"id1\", \"id2\"...]\n    const returnColumn = [];\n    // The outbind positions for the returning columns like [:3, :4, :5....]\n    const returnColumnBindPositions = [];\n    // Has the columns name in which data would be inserted like [\"id\", \"name\".....]\n    const insertColumns = [];\n    // Iterating over the allColumns keys to get the bindDef for inbind and outbinds\n    // and also to get the list of insert and return column after applying this.quoteIdentifier\n    for (const key of Object.keys(allColumns)) {\n      // If fieldMappenAttributes[attr] is defined we generate the bindDef \n      // and return clause else we can skip it\n      if (fieldMappedAttributes[key]) {\n        // BindDef for the specific column\n        const bindDef = fieldMappedAttributes[key].type._getBindDef(oracledb);\n        if (allColumns[key]) {\n          // Binddef for outbinds\n          bindDef.dir = oracledb.BIND_OUT;\n          outBindBindDefMap[key] = bindDef;\n\n          // Building the outbind parameter list\n          // ReturnColumn has the column name for example \"id\", \"usedId\", quoting depends on quoteIdentifier option\n          returnColumn.push(this.quoteIdentifier(key));\n          // Pushing the outbind index to the returnColumnPositions to generate (:3, :4, :5)\n          // The start offset depend on the tuple length (bind array size of a particular row)\n          // the outbind position starts after the position where inbind position ends\n          returnColumnBindPositions.push(`:${tuples[0].length + returnColumn.length}`);\n        } else {\n          // Binddef for inbinds\n          bindDef.dir = oracledb.BIND_IN;\n          inBindBindDefMap[key] = bindDef;\n        }\n      }\n      // Quoting and pushing each insert column based on quoteIdentifier option\n      insertColumns.push(this.quoteIdentifier(key));\n    }\n   \n    // Generating the sql query\n    let query = Utils.joinSQLFragments([\n      'INSERT',\n      'INTO',\n      // Table name for the table in which data needs to inserted\n      this.quoteTable(tableName),\n      // Columns names for the columns of the table (example \"a\", \"b\", \"c\" - quoting depends on the quoteidentifier option)\n      `(${insertColumns.join(',')})`,\n      'VALUES',\n      // InBind position for the insert query (for example :1, :2, :3....)\n      `(${inBindPosition})`\n    ]);\n\n    // If returnColumn.length is > 0\n    // then the returning into clause is needed\n    if (returnColumn.length > 0) {\n      options.outBindAttributes = outBindBindDefMap;\n      query = Utils.joinSQLFragments([\n        query,\n        'RETURNING',\n        // List of return column (for example \"id\", \"userId\"....)\n        `${returnColumn.join(',')}`,\n        'INTO',\n        // List of outbindPosition (for example :4, :5, :6....)\n        // Start offset depends on where inbindPosition end\n        `${returnColumnBindPositions}`\n      ]);\n    }\n\n    // Binding the bind variable to result\n    const result = { query };\n    // Binding the bindParam to result\n    // Tuple has each row for the insert query\n    result.bind = tuples;\n    // Setting options.inbindAttribute\n    options.inbindAttributes = inBindBindDefMap;\n    return result;\n  }\n\n  truncateTableQuery(tableName) {\n    return `TRUNCATE TABLE ${this.quoteTable(tableName)}`;\n  }\n\n  deleteQuery(tableName, where, options, model) {\n    options = options || {};\n\n    const table = tableName;\n\n    where = this.getWhereConditions(where, null, model, options);\n    let queryTmpl;\n    // delete with limit <l> and optional condition <e> on Oracle: DELETE FROM <t> WHERE rowid in (SELECT rowid FROM <t> WHERE <e> AND rownum <= <l>)\n    // Note that the condition <e> has to be in the subquery; otherwise, the subquery would select <l> arbitrary rows.\n    if (options.limit) {\n      const whereTmpl = where ? ` AND ${where}` : '';\n      queryTmpl =\n        `DELETE FROM ${this.quoteTable(table)} WHERE rowid IN (SELECT rowid FROM ${this.quoteTable(table)} WHERE rownum <= ${this.escape(options.limit)}${ \n          whereTmpl \n        })`;\n    } else {\n      const whereTmpl = where ? ` WHERE ${where}` : '';\n      queryTmpl = `DELETE FROM ${this.quoteTable(table)}${whereTmpl}`;\n    }\n    return queryTmpl;\n  }\n\n  showIndexesQuery(table) {\n    const [tableName, owner] = this.getSchemaNameAndTableName(table);\n    const sql = [\n      'SELECT i.index_name,i.table_name, i.column_name, u.uniqueness, i.descend, c.constraint_type ',\n      'FROM all_ind_columns i ',\n      'INNER JOIN all_indexes u ',\n      'ON (u.table_name = i.table_name AND u.index_name = i.index_name) ',\n      'LEFT OUTER JOIN all_constraints c ',\n      'ON (c.table_name = i.table_name AND c.index_name = i.index_name) ',\n      `WHERE i.table_name = ${this.escape(tableName)}`,\n      ' AND u.table_owner = ',\n      owner ? this.escape(owner) : 'USER',\n      ' ORDER BY index_name, column_position'\n    ];\n\n    return sql.join('');\n  }\n\n  removeIndexQuery(tableName, indexNameOrAttributes) {\n    let indexName = indexNameOrAttributes;\n\n    if (typeof indexName !== 'string') {\n      indexName = Utils.underscore(`${tableName }_${indexNameOrAttributes.join('_')}`);\n    }\n\n    return `DROP INDEX ${this.quoteIdentifier(indexName)}`;\n  }\n\n  attributeToSQL(attribute, options) {\n    if (!_.isPlainObject(attribute)) {\n      attribute = {\n        type: attribute\n      };\n    }\n\n    // TODO: Address on update cascade issue whether to throw error or ignore.\n    // Add this to documentation when merging to sequelize-main\n    // ON UPDATE CASCADE IS NOT SUPPORTED BY ORACLE.\n    attribute.onUpdate = '';\n\n    // handle self referential constraints\n    if (attribute.references) {\n      if (attribute.Model && attribute.Model.tableName === attribute.references.model) {\n        this.sequelize.log(\n          'Oracle does not support self referencial constraints, ' +\n            'we will remove it but we recommend restructuring your query'\n        );\n        attribute.onDelete = '';\n      }\n    }\n\n    let template;\n\n    template = attribute.type.toSql ? attribute.type.toSql() : '';\n    if (attribute.type instanceof DataTypes.JSON) {\n      template += ` CHECK (${this.quoteIdentifier(options.attributeName)} IS JSON)`;\n      return template;\n    }\n    if (Utils.defaultValueSchemable(attribute.defaultValue)) {\n      template += ` DEFAULT ${this.escape(attribute.defaultValue)}`;\n    }\n    if (attribute.allowNull === false) {\n      template += ' NOT NULL';\n    }\n    if (attribute.type instanceof DataTypes.ENUM) {\n      if (attribute.type.values && !attribute.values) attribute.values = attribute.type.values;\n      // enums are a special case\n      template +=\n        ` CHECK (${this.quoteIdentifier(options.attributeName)} IN(${ \n          _.map(attribute.values, value => {\n            return this.escape(value);\n          }).join(', ') \n        }))`;\n      return template;\n    } \n    if (attribute.type instanceof DataTypes.BOOLEAN) {\n      template +=\n        ` CHECK (${this.quoteIdentifier(options.attributeName)} IN('1', '0'))`;\n      return template;\n    } \n    if (attribute.autoIncrement) {\n      template = ' NUMBER(*,0) GENERATED BY DEFAULT ON NULL AS IDENTITY';\n    } else if (attribute.type && attribute.type.key === DataTypes.DOUBLE.key) {\n      template = attribute.type.toSql();\n    } else if (attribute.type) {\n      // setting it to false because oracle doesn't support unsigned int so put a check to make it behave like unsigned int\n      let unsignedTemplate = '';\n      if (attribute.type._unsigned) {\n        attribute.type._unsigned = false;\n        unsignedTemplate += ` check(${this.quoteIdentifier(options.attributeName)} >= 0)`;\n      }\n      template = attribute.type.toString();\n\n      // Blobs/texts cannot have a defaultValue\n      if (\n        attribute.type &&\n        attribute.type !== 'TEXT' &&\n        attribute.type._binary !== true &&\n        Utils.defaultValueSchemable(attribute.defaultValue)\n      ) {\n        template += ` DEFAULT ${this.escape(attribute.defaultValue)}`;\n      }\n\n      if (!attribute.autoIncrement) {\n        // If autoincrement, not null is set automatically\n        if (attribute.allowNull === false) {\n          template += ' NOT NULL';\n        } else if (!attribute.primaryKey && !Utils.defaultValueSchemable(attribute.defaultValue)) {\n          template += ' NULL';\n        }\n      }\n      template += unsignedTemplate;\n    } else {\n      template = '';\n    }\n\n    if (attribute.unique === true && !attribute.primaryKey) {\n      template += ' UNIQUE';\n    }\n\n    if (attribute.primaryKey) {\n      template += ' PRIMARY KEY';\n    }\n\n    if ((!options || !options.withoutForeignKeyConstraints) && attribute.references) {\n      template += ` REFERENCES ${this.quoteTable(attribute.references.model)}`;\n\n      if (attribute.references.key) {\n        template += ` (${this.quoteIdentifier(attribute.references.key) })`;\n      } else {\n        template += ` (${this.quoteIdentifier('id') })`;\n      }\n\n      if (attribute.onDelete && attribute.onDelete.toUpperCase() !== 'NO ACTION') {\n        template += ` ON DELETE ${attribute.onDelete.toUpperCase()}`;\n      }\n    }\n\n    return template;\n  }\n  attributesToSQL(attributes, options) {\n    const result = {};\n\n    for (const key in attributes) {\n      const attribute = attributes[key];\n      const attributeName = attribute.field || key;\n      result[attributeName] = this.attributeToSQL(attribute, { attributeName, ...options });\n    }\n\n    return result;\n  }\n\n  createTrigger() {\n    throwMethodUndefined('createTrigger');\n  }\n\n  dropTrigger() {\n    throwMethodUndefined('dropTrigger');\n  }\n\n  renameTrigger() {\n    throwMethodUndefined('renameTrigger');\n  }\n\n  createFunction() {\n    throwMethodUndefined('createFunction');\n  }\n\n  dropFunction() {\n    throwMethodUndefined('dropFunction');\n  }\n\n  renameFunction() {\n    throwMethodUndefined('renameFunction');\n  }\n\n  getConstraintsOnColumn(table, column) {\n    const [tableName, schemaName] = this.getSchemaNameAndTableName(table);\n    column = this.getCatalogName(column);\n    const sql = [\n      'SELECT CONSTRAINT_NAME FROM user_cons_columns WHERE TABLE_NAME = ',\n      this.escape(tableName),\n      ' and OWNER = ',\n      table.schema ? this.escape(schemaName) : 'USER',\n      ' and COLUMN_NAME = ',\n      this.escape(column),\n      ' AND POSITION IS NOT NULL ORDER BY POSITION'\n    ].join('');\n\n    return sql;\n  }\n\n  getForeignKeysQuery(table) {\n    // We don't call quoteTable as we don't want the schema in the table name, Oracle seperates it on another field\n    const [tableName, schemaName] = this.getSchemaNameAndTableName(table);\n    const sql = [\n      'SELECT DISTINCT  a.table_name \"tableName\", a.constraint_name \"constraintName\", a.owner \"owner\",  a.column_name \"columnName\",', \n      ' b.table_name \"referencedTableName\", b.column_name \"referencedColumnName\"',\n      ' FROM all_cons_columns a',\n      ' JOIN all_constraints c ON a.owner = c.owner AND a.constraint_name = c.constraint_name',\n      ' JOIN all_cons_columns b ON c.owner = b.owner AND c.r_constraint_name = b.constraint_name',\n      \" WHERE c.constraint_type  = 'R'\",\n      ' AND a.table_name = ',\n      this.escape(tableName),\n      ' AND a.owner = ',\n      table.schema ? this.escape(schemaName) : 'USER',\n      ' ORDER BY a.table_name, a.constraint_name'\n    ].join('');\n\n    return sql;\n  }\n\n  dropForeignKeyQuery(tableName, foreignKey) {\n    return this.dropConstraintQuery(tableName, foreignKey);\n  }\n\n  getPrimaryKeyConstraintQuery(table) {\n    const [tableName, schemaName] = this.getSchemaNameAndTableName(table);\n    const sql = [\n      'SELECT cols.column_name, atc.identity_column ',\n      'FROM all_constraints cons, all_cons_columns cols ',\n      'INNER JOIN all_tab_columns atc ON(atc.table_name = cols.table_name AND atc.COLUMN_NAME = cols.COLUMN_NAME )',\n      'WHERE cols.table_name = ',\n      this.escape(tableName),\n      'AND cols.owner = ',\n      table.schema ? this.escape(schemaName) : 'USER ',\n      \"AND cons.constraint_type = 'P' \",\n      'AND cons.constraint_name = cols.constraint_name ',\n      'AND cons.owner = cols.owner ',\n      'ORDER BY cols.table_name, cols.position'\n    ].join('');\n\n    return sql;\n  }\n\n  dropConstraintQuery(tableName, constraintName) {\n    return `ALTER TABLE ${this.quoteTable(tableName)} DROP CONSTRAINT ${constraintName}`;\n  }\n\n  setIsolationLevelQuery(value, options) {\n    if (options.parent) {\n      return;\n    }\n\n    switch (value) {\n      case Transaction.ISOLATION_LEVELS.READ_UNCOMMITTED:\n      case Transaction.ISOLATION_LEVELS.READ_COMMITTED:\n        return 'SET TRANSACTION ISOLATION LEVEL READ COMMITTED;';\n      case Transaction.ISOLATION_LEVELS.REPEATABLE_READ:\n        // Serializable mode is equal to Snapshot Isolation (SI) \n        // defined in ANSI std.\n        return 'SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;';\n      default:\n        throw new Error(`isolation level \"${value}\" is not supported`);\n    }\n  }\n\n  getAliasToken() {\n    return '';\n  }\n\n  startTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return `SAVEPOINT ${this.quoteIdentifier(transaction.name)}`;\n    }\n\n    return 'BEGIN TRANSACTION';\n  }\n\n  commitTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return;\n    }\n\n    return 'COMMIT TRANSACTION';\n  }\n\n  rollbackTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return `ROLLBACK TO SAVEPOINT ${this.quoteIdentifier(transaction.name)}`;\n    }\n\n    return 'ROLLBACK TRANSACTION';\n  }\n\n  handleSequelizeMethod(smth, tableName, factory, options, prepend) {\n    let str;\n    if (smth instanceof Utils.Json) {\n      // Parse nested object\n      if (smth.conditions) {\n        const conditions = this.parseConditionObject(smth.conditions).map(condition =>\n          `${this.jsonPathExtractionQuery(condition.path[0], _.tail(condition.path))} = '${condition.value}'`\n        );\n\n        return conditions.join(' AND ');\n      }\n      if (smth.path) {\n\n        // Allow specifying conditions using the sqlite json functions\n        if (this._checkValidJsonStatement(smth.path)) {\n          str = smth.path;\n        } else {\n          // Also support json property accessors\n          const paths = _.toPath(smth.path);\n          const column = paths.shift();\n          str = this.jsonPathExtractionQuery(column, paths);\n        }\n        if (smth.value) {\n          str += util.format(' = %s', this.escape(smth.value));\n        }\n\n        return str;\n      }\n    }\n    if (smth instanceof Utils.Cast) {\n      if (smth.val instanceof Utils.SequelizeMethod) {\n        str = this.handleSequelizeMethod(smth.val, tableName, factory, options, prepend);\n        if (smth.type === 'boolean') {\n          str = `(CASE WHEN ${str}='true' THEN 1 ELSE 0 END)`;\n          return `CAST(${str} AS NUMBER)`;\n        } if (smth.type === 'timestamptz' && /json_value\\(/.test(str)) {\n          str = str.slice(0, -1);\n          return `${str} RETURNING TIMESTAMP WITH TIME ZONE)`;\n        }\n      }\n    }\n    return super.handleSequelizeMethod(smth, tableName, factory, options, prepend);\n  }\n\n  _checkValidJsonStatement(stmt) {\n    if (typeof stmt !== 'string') {\n      return false;\n    }\n\n    let currentIndex = 0;\n    let openingBrackets = 0;\n    let closingBrackets = 0;\n    let hasJsonFunction = false;\n    let hasInvalidToken = false;\n\n    while (currentIndex < stmt.length) {\n      const string = stmt.substr(currentIndex);\n      const functionMatches = JSON_FUNCTION_REGEX.exec(string);\n      if (functionMatches) {\n        currentIndex += functionMatches[0].indexOf('(');\n        hasJsonFunction = true;\n        continue;\n      }\n\n      const operatorMatches = JSON_OPERATOR_REGEX.exec(string);\n      if (operatorMatches) {\n        currentIndex += operatorMatches[0].length;\n        hasJsonFunction = true;\n        continue;\n      }\n\n      const tokenMatches = TOKEN_CAPTURE_REGEX.exec(string);\n      if (tokenMatches) {\n        const capturedToken = tokenMatches[1];\n        if (capturedToken === '(') {\n          openingBrackets++;\n        } else if (capturedToken === ')') {\n          closingBrackets++;\n        } else if (capturedToken === ';') {\n          hasInvalidToken = true;\n          break;\n        }\n        currentIndex += tokenMatches[0].length;\n        continue;\n      }\n\n      break;\n    }\n\n    // Check invalid json statement\n    if (hasJsonFunction && (hasInvalidToken || openingBrackets !== closingBrackets)) {\n      throw new Error(`Invalid json statement: ${stmt}`);\n    }\n\n    // return true if the statement has valid json function\n    return hasJsonFunction;\n  }\n\n  jsonPathExtractionQuery(column, path) {\n    let paths = _.toPath(path);\n    const quotedColumn = this.isIdentifierQuoted(column) ? column : this.quoteIdentifier(column);\n\n    paths = paths.map(subPath => {\n      return /\\D/.test(subPath) ? Utils.addTicks(subPath, '\"') : subPath;\n    });\n\n    const pathStr = this.escape(['$'].concat(paths).join('.').replace(/\\.(\\d+)(?:(?=\\.)|$)/g, (__, digit) => `[${digit}]`));\n\n    return `json_value(${quotedColumn},${pathStr})`;\n  }\n\n  addLimitAndOffset(options, model) {\n    let fragment = '';\n    const offset = options.offset || 0,\n      isSubQuery = options.hasIncludeWhere || options.hasIncludeRequired || options.hasMultiAssociation;\n\n    let orders = {};\n    if (options.order) {\n      orders = this.getQueryOrders(options, model, isSubQuery);\n    }\n\n    if (options.limit || options.offset) {\n      // Add needed order by clause only when it is not provided\n      if (!orders.mainQueryOrder || !orders.mainQueryOrder.length || isSubQuery && (!orders.subQueryOrder || !orders.subQueryOrder.length)) {\n        const tablePkFragment = `${this.quoteTable(options.tableAs || model.name)}.${this.quoteIdentifier(model.primaryKeyField)}`;\n        fragment += ` ORDER BY ${tablePkFragment}`;\n      }\n\n      if (options.offset || options.limit) {\n        fragment += ` OFFSET ${this.escape(offset)} ROWS`;\n      }\n\n      if (options.limit) {\n        fragment += ` FETCH NEXT ${this.escape(options.limit)} ROWS ONLY`;\n      }\n    }\n\n    return fragment;\n  }\n\n  booleanValue(value) {\n    return value ? 1 : 0;\n  }\n\n  quoteIdentifier(identifier, force = false) {\n    const optForceQuote = force;\n    const optQuoteIdentifiers = this.options.quoteIdentifiers !== false;\n    const rawIdentifier = Utils.removeTicks(identifier, '\"');\n    const regExp = /^(([\\w][\\w\\d_]*))$/g;\n\n    if (\n      optForceQuote !== true &&\n      optQuoteIdentifiers === false &&\n      regExp.test(rawIdentifier) &&\n      !ORACLE_RESERVED_WORDS.includes(rawIdentifier.toUpperCase())\n    ) {\n      // In Oracle, if tables, attributes or alias are created double-quoted,\n      // they are always case sensitive. If they contain any lowercase\n      // characters, they must always be double-quoted otherwise it\n      // would get uppercased by the DB.\n      // Here, we strip quotes if we don't want case sensitivity.\n      return rawIdentifier;\n    }\n    return Utils.addTicks(rawIdentifier, '\"');\n  }\n\n  /**\n * It causes bindbyPosition like :1, :2, :3\n * We pass the val parameter so that the outBind indexes\n * starts after the inBind indexes end\n *\n * @param {Array} bind\n * @param {number} posOffset\n */\n  bindParam(bind, posOffset = 0) {\n    return value => {\n      bind.push(value);\n      return `:${bind.length + posOffset}`;\n    };\n  }\n\n  /**\n   * Returns the authenticate test query string\n   */\n  authTestQuery() {\n    return 'SELECT 1+1 AS result FROM DUAL';\n  }\n}\n\n/* istanbul ignore next */\nfunction throwMethodUndefined(methodName) {\n  throw new Error(`The method \"${methodName}\" is not defined! Please add it to your sql dialect.`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAIA,MAAM,QAAQ,QAAQ;AACtB,MAAM,YAAY,QAAQ;AAC1B,MAAM,yBAAyB,QAAQ;AACvC,MAAM,IAAI,QAAQ;AAClB,MAAM,OAAO,QAAQ;AACrB,MAAM,cAAc,QAAQ;AAQ5B,MAAM,wBAAwB,CAAC,UAAU,OAAO,OAAO,SAAS,OAAO,OAAO,YAAY,MAAM,OAAO,SAAS,WAAW,MAAM,QAAQ,SAAS,WAAW,UAAU,WAAW,YAAY,WAAW,UAAU,WAAW,QAAQ,WAAW,WAAW,UAAU,QAAQ,YAAY,QAAQ,QAAQ,aAAa,UAAU,QAAQ,SAAS,OAAO,QAAQ,SAAS,SAAS,UAAU,cAAc,aAAa,MAAM,aAAa,SAAS,WAAW,UAAU,WAAW,aAAa,QAAQ,MAAM,SAAS,QAAQ,QAAQ,QAAQ,cAAc,SAAS,QAAQ,UAAU,WAAW,cAAc,OAAO,YAAY,UAAU,QAAQ,UAAU,MAAM,WAAW,MAAM,UAAU,UAAU,MAAM,SAAS,WAAW,SAAS,cAAc,UAAU,OAAO,UAAU,YAAY,UAAU,OAAO,SAAS,YAAY,UAAU,QAAQ,UAAU,WAAW,OAAO,SAAS,QAAQ,YAAY,UAAU,SAAS,cAAc,WAAW,WAAW,SAAS,QAAQ,MAAM,WAAW,OAAO,SAAS,UAAU,UAAU,QAAQ,YAAY,UAAU,WAAW,YAAY,QAAQ,YAAY,SAAS;AACpkC,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAErB,mCAAmC,uBAAuB;AAAA,EAC/D,YAAY,SAAS;AACnB,UAAM;AAAA;AAAA,EAQR,eAAe,OAAO;AACpB,QAAI,OAAO;AACT,UAAI,KAAK,QAAQ,qBAAqB,OAAO;AAC3C,cAAM,cAAc,KAAK,gBAAgB;AACzC,YAAI,gBAAgB,OAAO;AACzB,kBAAQ,MAAM;AAAA;AAAA;AAAA;AAIpB,WAAO;AAAA;AAAA,EAQT,0BAA0B,OAAO;AAC/B,UAAM,YAAY,KAAK,eAAe,MAAM,aAAa;AACzD,UAAM,aAAa,KAAK,eAAe,MAAM;AAC7C,WAAO,CAAC,WAAW;AAAA;AAAA,EAGrB,aAAa,QAAQ;AACnB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,OAAO,eAAe;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,OAAO,sBAAsB;AAAA,MAClC;AAAA,MACA;AAAA,MACA,KAAK,OAAO,yBAAyB;AAAA,MACrC;AAAA,MACA;AAAA,MACA,KAAK,OAAO,wBAAwB;AAAA,MACpC;AAAA,MACA;AAAA,MACA,KAAK,OAAO,+BAA+B;AAAA,MAC3C;AAAA,MACA;AAAA,MACA,KAAK,OAAO,iCAAiC;AAAA,MAC7C;AAAA,MACA;AAAA,MACA,KAAK,OAAO,4BAA4B;AAAA,MACxC;AAAA,MACA;AAAA,MACA,KAAK,OAAO,2BAA2B;AAAA,MACvC;AAAA,MACA;AAAA,MACA,KAAK,OAAO,cAAc;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,WAAW,QAAQ;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK,OAAO,aAAa,KAAK,WAAW;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,iBAAiB,WAAW,YAAY,SAAS;AAC/C,UAAM,cAAc,IAClB,cAAc,OAAO,OAAO,OAC5B,UAAU,IACV,WAAW;AAEb,UAAM,SAAS;AAAA,MACb,OAAO,KAAK,WAAW;AAAA;AAIzB,aAAS,QAAQ,YAAY;AAC3B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,YAAY;AAAO;AAC7D,YAAM,WAAW,WAAW;AAC5B,aAAO,KAAK,gBAAgB;AAG5B,UAAI,SAAS,SAAS,gBAAgB;AAEpC,oBAAY,KAAK;AACjB,YAAI,SAAS,SAAS,eAAe;AACnC,gBAAM,QAAQ,SAAS,MAAM;AAC7B,kBAAQ,KAAK,GAAG,QAAQ,MAAM,GAAG,QAAQ,eAAe;AAGxD,sBAAY,QAAQ,MAAM;AAAA,eACrB;AACL,kBAAQ,KAAK,GAAG,QAAQ,SAAS,QAAQ,eAAe,IAAI;AAAA;AAAA,iBAErD,SAAS,SAAS,eAAe;AAE1C,cAAM,QAAQ,SAAS,MAAM;AAC7B,gBAAQ,KAAK,GAAG,QAAQ,MAAM;AAG9B,oBAAY,QAAQ,MAAM;AAAA,aACrB;AACL,gBAAQ,KAAK,GAAG,QAAQ;AAAA;AAAA;AAI5B,WAAO,gBAAgB,QAAQ,KAAK;AAEpC,UAAM,WAAW,YAAY,IAAI,QAAM,KAAK,gBAAgB,KAAK,KAAK;AAEtE,QAAI,SAAS,SAAS,GAAG;AACvB,aAAO,cAAc,iBAAiB;AAAA;AAIxC,eAAW,QAAQ,aAAa;AAC9B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa;AAAO;AAE9D,UAAI,YAAY,MAAM,QAAQ,yBAAyB,IAAI;AACzD,oBAAY,QAAQ,YAAY,MAAM,QAAQ,uBAAuB;AAAA;AAEvE,aAAO,cAAc,iBAAiB,KAAK,gBAAgB,UAAU,YAAY;AAAA;AAGnF,QAAI,SAAS,SAAS,GAAG;AACvB,aAAO,cAAc,KAAK,SAAS,KAAK;AAAA;AAI1C,QAAI,WAAW,QAAQ,WAAW,QAAQ,QAAQ,SAAS,GAAG;AAC5D,YAAM,cAAc;AACpB,cAAQ,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACtC,YAAI,YAAY,SAAU,OAAM,WAAW,QAAQ,MAAM,OAAO,SAAS,KAAK,MAAM,WAAW,QAAQ;AAErG,gBAAM,SAAS,MAAM,OAAO,IAAI,WAAS;AACvC,gBAAI,OAAO,UAAU,UAAU;AAC7B,qBAAO;AAAA;AAET,mBAAO,MAAM;AAAA;AAKf,cAAI,cAAc;AAClB,cAAI,QAAQ,YAAY;AACtB,kBAAM,OAAO,OAAO,KAAK,QAAQ;AAEjC,qBAAS,WAAW,GAAG,WAAW,KAAK,QAAQ,YAAY;AACzD,oBAAM,aAAa,QAAQ,WAAW,KAAK;AAE3C,kBAAI,WAAW,OAAO,WAAW,OAAO,QAAQ;AAE9C,yBAAS,IAAI,GAAG,IAAI,WAAW,OAAO,QAAQ,KAAK;AACjD,wBAAM,QAAQ,WAAW,OAAO;AAEhC,sBAAI,EAAE,SAAS,QAAQ,QAAQ;AAC7B,kCAAc;AAAA,yBACT;AAEL,kCAAc;AACd;AAAA;AAAA;AAAA;AAAA;AAMR,gBAAI,aAAa;AACf,oBAAM,YAAY,UAAU,QAAQ,MAAM,OAAO;AACjD,oBAAM,kBAAkB;AAAA,gBACtB,MAAM;AAAA,gBACN;AAAA;AAEF,kBAAI,CAAE,iBAAgB,UAAU;AAC9B,wBAAQ,aAAa;AAAA;AAGvB,sBAAQ,WAAW,aAAa;AAChC,0BAAY,KAAK;AAAA,mBACZ;AAEL,0BAAY,KAAK;AAAA;AAAA;AAAA;AAAA;AAKzB,kBAAY,QAAQ,SAAO;AACzB,gBAAQ,QAAQ,OAAO,KAAK;AAAA;AAAA;AAIhC,QAAI,WAAW,CAAC,CAAC,QAAQ,YAAY;AACnC,QAAE,KAAK,QAAQ,YAAY,CAAC,SAAS,cAAc;AACjD,YAAI,YAAY;AAGhB,oBAAY,QAAQ,gBAAc;AAIhC,uBAAa,WAAW,QAAQ,MAAM;AAKtC,cAAI,CAAC,EAAE,SAAS,QAAQ,QAAQ,aAAa;AAC3C,wBAAY;AAAA;AAAA;AAIhB,gBAAQ,OAAO,QAAQ,WAAS;AAC9B,cAAI,YAAY;AAChB,cAAI,CAAC,EAAE,SAAS,QAAQ;AACtB,wBAAY,MAAM,UAAU,QAAQ,YAAY;AAAA,iBAC3C;AACL,wBAAY,MAAM,QAAQ,YAAY;AAAA;AAExC,cAAI,aAAa,YAAY;AAG3B,gBAAI,WAAW,WAAW,cAAc,QAAQ,YAAY,MAAM,WAAW;AAE3E,oBAAM,gBAAgB,WAAW,WAAW,QAAQ,UAAU;AAE9D,qBAAO,aAAa,OAAO,WAAW,QAAQ,WAAW,YAAY;AAAA;AAAA;AAAA;AAM3E,YAAI,WAAW;AACb,gBAAM,QAAQ,QAAQ,WAAW,QAAQ;AACzC,iBAAO,QAAQ,WAAW,QAAQ;AAClC,sBAAY,UAAU,QAAQ,WAAW;AACzC,kBAAQ,OAAO;AACf,kBAAQ,WAAW,aAAa;AAGhC,cAAI,UAAU,WAAW,GAAG;AAC1B,mBAAO,cAAc,YAAY,QAAQ,OAAO,IAAI,WAAS,KAAK,gBAAgB,QAAQ,KAAK;AAAA,iBAC1F;AACL,mBAAO,cACL,gBAAgB,KAAK,gBAAgB,sBAAsB,QAAQ,OAAO,IAAI,WAAS,KAAK,gBAAgB,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAOnI,UAAM,QAAQ,MAAM,iBAAiB;AAAA,MACnC;AAAA,MACA,OAAO;AAAA,MACP,IAAI,OAAO;AAAA;AAGb,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,GAAG,KAAK,OAAO;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,iBAAiB,OAAO;AACtB,UAAM,CAAC,WAAW,cAAc,KAAK,0BAA0B;AAC/D,WAAO,wDAAwD,KAAK,OAAO,0BAA0B,MAAM,SAAS,KAAK,OAAO,cAAc;AAAA;AAAA,EAGhJ,mBAAmB,WAAW,QAAQ;AACpC,UAAM,gBAAgB,KAAK,eAAe,UAAU,aAAa;AACjE,aAAS,KAAK,eAAe;AAE7B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SACI,sBAAsB,KAAK,OAAO,cAClC;AAAA,MACJ,yBAAyB,KAAK,OAAO;AAAA,MACrC;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,iBAAiB,QAAQ,OAAO;AAC9B,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,WAAW;AAAA;AAAA;AAAA,EAIpB,qBAAqB,OAAO;AAC1B,UAAM,YAAY,KAAK,eAAe,MAAM,aAAa;AACzD,WAAO,oFAAoF,KAAK,OAAO;AAAA;AAAA,EAGzG,kBAAkB;AAChB,WAAO;AAAA;AAAA,EAGT,eAAe,WAAW;AACxB,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,EASJ,cAAc,WAAW,YAAY,SAAS,cAAc;AAC1D,QAAI,OAAO,cAAc,YAAY,WAAW,MAAM;AACpD,iBAAW,OAAO,GAAG,UAAU,UAAU,WAAW;AAAA;AAEtD,WAAO,MAAM,cAAc,WAAW,YAAY,SAAS;AAAA;AAAA,EAG7D,mBAAmB,WAAW,SAAS;AACrC,cAAU,WAAW;AAErB,QAAI,QAAQ,UAAU;AAEpB,aAAO,QAAQ;AAAA;AAGjB,QAAI,QAAQ,YAAY,QAAQ,SAAS,kBAAkB,aAAa;AAEtE,aAAO,QAAQ;AAAA;AAGjB,UAAM,oBAAoB,KAAK,qBAAqB,WAAW;AAE/D,gBAAY,KAAK,WAAW;AAC5B,WAAO,eAAe,iBAAiB;AAAA;AAAA,EAGzC,eAAe,OAAO,KAAK,UAAU;AACnC,aAAS,QAAQ;AAEjB,UAAM,YAAY,MAAM,iBAAiB;AAAA,MACvC,KAAK,gBAAgB;AAAA,MACrB,KAAK,eAAe,UAAU;AAAA,QAC5B,eAAe;AAAA,QACf,SAAS;AAAA;AAAA;AAIb,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,eAAe;AAC1C,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAAA;AAAA,EAkBJ,2BAA2B,YAAY,OAAO,eAAe;AAC3D,UAAM,CAAC,WAAW,cAAc,KAAK,0BAA0B;AAC/D,UAAM,wBAAwB,KAAK,OAAO,KAAK,eAAe;AAC9D,UAAM,qBAAqB,MAAM,SAAS,KAAK,OAAO,KAAK,eAAe,eAAe;AACzF,UAAM,oBAAoB,KAAK,OAAO,KAAK,eAAe;AAC1D,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AACP,UAAM,cAAc,MAAM,iBAAiB;AAAA,MACzC,eAAe,KAAK,gBAAgB;AAAA,MACpC;AAAA,MACA,IAAI,KAAK,gBAAgB;AAAA,MACzB,WAAW,QAAQ,qBAAqB;AAAA;AAE1C,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,mCAAmC,KAAK,WAAW;AAAA,MACnD;AAAA,MACA,qBAAqB,KAAK,OAAO;AAAA,MACjC,KAAK;AAAA;AAAA,EAUT,aAAa,YAAY,OAAO,eAAe;AAC7C,iBAAa,WAAW,WAAW,UAAU,WAAW,QAAQ,SAAS,MAAM;AAC/E,UAAM,QAAQ,MAAM,iBAAiB;AAAA,MACnC;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAEF,UAAM,cAAc,MAAM,QAAQ,YAAY,IAAI,QAAQ,QAAQ;AAClE,WAAO;AAAA,MACL;AAAA,MACA,qBAAqB,KAAK,OAAO;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MAEA,wBAAwB,KAAK,OAAO;AAAA,MACpC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,kBAAkB,OAAO,YAAY;AACnC,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA;AAEF,eAAW,iBAAiB,YAAY;AACtC,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,YAAY;AAAgB;AACtE,YAAM,aAAa,WAAW;AAC9B,UAAI,WAAW,MAAM,eAAe;AAClC,YAAI,KAAK,KAAK,2BAA2B,YAAY,OAAO;AAAA,aACvD;AAEL,YAAI,KAAK,KAAK,aAAa,YAAY,OAAO;AAAA;AAAA;AAGlD,QAAI,KAAK;AACT,WAAO,IAAI,KAAK;AAAA;AAAA,EAGlB,kBAAkB,WAAW,YAAY,YAAY;AACnD,UAAM,UAAU,OAAO,KAAK,YAAY;AACxC,WAAO,eAAe,KAAK,WAAW,4BAA4B,KAAK,gBAAgB,kBAAkB,KAAK,gBAAgB;AAAA;AAAA,EAehI,mCAAmC,0BAA0B,aAAa,cAAc,kBAAkB,SAAS;AACjH,UAAM,WAAW,KAAK,UAAU,kBAAkB;AAClD,UAAM,oBAAoB,OAAO,OAAO;AACxC,UAAM,UAAU;AAChB,UAAM,eAAe,KAAK,UAAU,SAAS;AAC7C,6BAAyB,QAAQ,CAAC,SAAS,UAAU;AAGnD,UAAI,QAAQ,WAAW,MAAM;AAC3B,kBAAU,QAAQ,UAAU,GAAG,QAAQ,SAAS;AAAA;AAElD,wBAAkB,WAAW,OAAO,OAAO,YAAY,OAAO,YAAY,WAAW,EAAE,KAAK,SAAS;AACrG,YAAM,kBAAkB,GAAG,KAAK,OAAO,QAAW,QAAW,EAAE,SAAS,YAAY;AACpF,uBAAiB,KAAK;AAAA;AAExB,YAAQ,oBAAoB;AAAA;AAAA,EAc9B,YAAY,WAAW,cAAc,cAAc,OAAO,OAAO,SAAS;AACxE,UAAM,gBAAgB,MAAM;AAC5B,UAAM,cAAc,KAAK,YAAY,WAAW,cAAc,OAAO,SAAS;AAE9E,YAAQ,OAAO,YAAY;AAC3B,UAAM,cAAc,KAAK,YAAY,WAAW,cAAc,eAAe;AAE7E,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA,YAAY,QAAQ;AAAA,QAClB,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK,MAAM;AAAA,QACX,YAAY;AAAA,QACZ;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AAAA;AAGF,UAAM,QAAQ,IAAI,KAAK;AACvB,UAAM,SAAS,EAAE;AAEjB,QAAI,QAAQ,cAAc,OAAO;AAC/B,aAAO,OAAO,YAAY,QAAQ,YAAY;AAAA;AAGhD,WAAO;AAAA;AAAA,EAaT,gBAAgB,WAAW,kBAAkB,SAAS,uBAAuB;AAC3E,cAAU,WAAW;AACrB,YAAQ,cAAc;AACtB,4BAAwB,yBAAyB;AAEjD,UAAM,SAAS;AACf,UAAM,aAAa;AACnB,UAAM,mBAAmB;AACzB,UAAM,oBAAoB;AAC1B,UAAM,WAAW,KAAK,UAAU,kBAAkB;AAUlD,eAAW,kBAAkB,kBAAkB;AAC7C,QAAE,OAAO,gBAAgB,CAAC,OAAO,QAAQ;AACvC,mBAAW,OAAO,sBAAsB,QAAQ,sBAAsB,KAAK,kBAAkB,QAAQ,UAAU;AAAA;AAAA;AAMnH,QAAI;AAEJ,eAAW,kBAAkB,kBAAkB;AAG7C,YAAM,QAAQ;AAId,YAAM,cAAc,QAAQ,cAAc,SAAY,KAAK,UAAU,SAAS,QAAQ;AAKtF,YAAM,oBAAoB,OAAO,KAAK,YAAY,IAAI,SAAO;AAC3D,YAAI,WAAW,SAAS,MAAM;AAI5B,cAAI,eAAe,SAAS,MAAM;AAChC,kBAAM,MAAM;AAAA;AAGd,iBAAO;AAAA;AAIT,eAAO,KAAK,OAAO,eAAe,MAAM,sBAAsB,MAAM,EAAE,SAAS,YAAY;AAAA;AAK7F,UAAI,CAAC,gBAAgB;AACnB,yBAAiB;AAAA;AAGnB,aAAO,KAAK;AAAA;AAId,UAAM,eAAe;AAErB,UAAM,4BAA4B;AAElC,UAAM,gBAAgB;AAGtB,eAAW,OAAO,OAAO,KAAK,aAAa;AAGzC,UAAI,sBAAsB,MAAM;AAE9B,cAAM,UAAU,sBAAsB,KAAK,KAAK,YAAY;AAC5D,YAAI,WAAW,MAAM;AAEnB,kBAAQ,MAAM,SAAS;AACvB,4BAAkB,OAAO;AAIzB,uBAAa,KAAK,KAAK,gBAAgB;AAIvC,oCAA0B,KAAK,IAAI,OAAO,GAAG,SAAS,aAAa;AAAA,eAC9D;AAEL,kBAAQ,MAAM,SAAS;AACvB,2BAAiB,OAAO;AAAA;AAAA;AAI5B,oBAAc,KAAK,KAAK,gBAAgB;AAAA;AAI1C,QAAI,QAAQ,MAAM,iBAAiB;AAAA,MACjC;AAAA,MACA;AAAA,MAEA,KAAK,WAAW;AAAA,MAEhB,IAAI,cAAc,KAAK;AAAA,MACvB;AAAA,MAEA,IAAI;AAAA;AAKN,QAAI,aAAa,SAAS,GAAG;AAC3B,cAAQ,oBAAoB;AAC5B,cAAQ,MAAM,iBAAiB;AAAA,QAC7B;AAAA,QACA;AAAA,QAEA,GAAG,aAAa,KAAK;AAAA,QACrB;AAAA,QAGA,GAAG;AAAA;AAAA;AAKP,UAAM,SAAS,EAAE;AAGjB,WAAO,OAAO;AAEd,YAAQ,mBAAmB;AAC3B,WAAO;AAAA;AAAA,EAGT,mBAAmB,WAAW;AAC5B,WAAO,kBAAkB,KAAK,WAAW;AAAA;AAAA,EAG3C,YAAY,WAAW,OAAO,SAAS,OAAO;AAC5C,cAAU,WAAW;AAErB,UAAM,QAAQ;AAEd,YAAQ,KAAK,mBAAmB,OAAO,MAAM,OAAO;AACpD,QAAI;AAGJ,QAAI,QAAQ,OAAO;AACjB,YAAM,YAAY,QAAQ,QAAQ,UAAU;AAC5C,kBACE,eAAe,KAAK,WAAW,4CAA4C,KAAK,WAAW,0BAA0B,KAAK,OAAO,QAAQ,SACvI;AAAA,WAEC;AACL,YAAM,YAAY,QAAQ,UAAU,UAAU;AAC9C,kBAAY,eAAe,KAAK,WAAW,SAAS;AAAA;AAEtD,WAAO;AAAA;AAAA,EAGT,iBAAiB,OAAO;AACtB,UAAM,CAAC,WAAW,SAAS,KAAK,0BAA0B;AAC1D,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,wBAAwB,KAAK,OAAO;AAAA,MACpC;AAAA,MACA,QAAQ,KAAK,OAAO,SAAS;AAAA,MAC7B;AAAA;AAGF,WAAO,IAAI,KAAK;AAAA;AAAA,EAGlB,iBAAiB,WAAW,uBAAuB;AACjD,QAAI,YAAY;AAEhB,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,MAAM,WAAW,GAAG,aAAc,sBAAsB,KAAK;AAAA;AAG3E,WAAO,cAAc,KAAK,gBAAgB;AAAA;AAAA,EAG5C,eAAe,WAAW,SAAS;AACjC,QAAI,CAAC,EAAE,cAAc,YAAY;AAC/B,kBAAY;AAAA,QACV,MAAM;AAAA;AAAA;AAOV,cAAU,WAAW;AAGrB,QAAI,UAAU,YAAY;AACxB,UAAI,UAAU,SAAS,UAAU,MAAM,cAAc,UAAU,WAAW,OAAO;AAC/E,aAAK,UAAU,IACb;AAGF,kBAAU,WAAW;AAAA;AAAA;AAIzB,QAAI;AAEJ,eAAW,UAAU,KAAK,QAAQ,UAAU,KAAK,UAAU;AAC3D,QAAI,UAAU,gBAAgB,UAAU,MAAM;AAC5C,kBAAY,WAAW,KAAK,gBAAgB,QAAQ;AACpD,aAAO;AAAA;AAET,QAAI,MAAM,sBAAsB,UAAU,eAAe;AACvD,kBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAEhD,QAAI,UAAU,cAAc,OAAO;AACjC,kBAAY;AAAA;AAEd,QAAI,UAAU,gBAAgB,UAAU,MAAM;AAC5C,UAAI,UAAU,KAAK,UAAU,CAAC,UAAU;AAAQ,kBAAU,SAAS,UAAU,KAAK;AAElF,kBACE,WAAW,KAAK,gBAAgB,QAAQ,qBACtC,EAAE,IAAI,UAAU,QAAQ,WAAS;AAC/B,eAAO,KAAK,OAAO;AAAA,SAClB,KAAK;AAEZ,aAAO;AAAA;AAET,QAAI,UAAU,gBAAgB,UAAU,SAAS;AAC/C,kBACE,WAAW,KAAK,gBAAgB,QAAQ;AAC1C,aAAO;AAAA;AAET,QAAI,UAAU,eAAe;AAC3B,iBAAW;AAAA,eACF,UAAU,QAAQ,UAAU,KAAK,QAAQ,UAAU,OAAO,KAAK;AACxE,iBAAW,UAAU,KAAK;AAAA,eACjB,UAAU,MAAM;AAEzB,UAAI,mBAAmB;AACvB,UAAI,UAAU,KAAK,WAAW;AAC5B,kBAAU,KAAK,YAAY;AAC3B,4BAAoB,UAAU,KAAK,gBAAgB,QAAQ;AAAA;AAE7D,iBAAW,UAAU,KAAK;AAG1B,UACE,UAAU,QACV,UAAU,SAAS,UACnB,UAAU,KAAK,YAAY,QAC3B,MAAM,sBAAsB,UAAU,eACtC;AACA,oBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAGhD,UAAI,CAAC,UAAU,eAAe;AAE5B,YAAI,UAAU,cAAc,OAAO;AACjC,sBAAY;AAAA,mBACH,CAAC,UAAU,cAAc,CAAC,MAAM,sBAAsB,UAAU,eAAe;AACxF,sBAAY;AAAA;AAAA;AAGhB,kBAAY;AAAA,WACP;AACL,iBAAW;AAAA;AAGb,QAAI,UAAU,WAAW,QAAQ,CAAC,UAAU,YAAY;AACtD,kBAAY;AAAA;AAGd,QAAI,UAAU,YAAY;AACxB,kBAAY;AAAA;AAGd,QAAK,EAAC,WAAW,CAAC,QAAQ,iCAAiC,UAAU,YAAY;AAC/E,kBAAY,eAAe,KAAK,WAAW,UAAU,WAAW;AAEhE,UAAI,UAAU,WAAW,KAAK;AAC5B,oBAAY,KAAK,KAAK,gBAAgB,UAAU,WAAW;AAAA,aACtD;AACL,oBAAY,KAAK,KAAK,gBAAgB;AAAA;AAGxC,UAAI,UAAU,YAAY,UAAU,SAAS,kBAAkB,aAAa;AAC1E,oBAAY,cAAc,UAAU,SAAS;AAAA;AAAA;AAIjD,WAAO;AAAA;AAAA,EAET,gBAAgB,YAAY,SAAS;AACnC,UAAM,SAAS;AAEf,eAAW,OAAO,YAAY;AAC5B,YAAM,YAAY,WAAW;AAC7B,YAAM,gBAAgB,UAAU,SAAS;AACzC,aAAO,iBAAiB,KAAK,eAAe,WAAW,iBAAE,iBAAkB;AAAA;AAG7E,WAAO;AAAA;AAAA,EAGT,gBAAgB;AACd,yBAAqB;AAAA;AAAA,EAGvB,cAAc;AACZ,yBAAqB;AAAA;AAAA,EAGvB,gBAAgB;AACd,yBAAqB;AAAA;AAAA,EAGvB,iBAAiB;AACf,yBAAqB;AAAA;AAAA,EAGvB,eAAe;AACb,yBAAqB;AAAA;AAAA,EAGvB,iBAAiB;AACf,yBAAqB;AAAA;AAAA,EAGvB,uBAAuB,OAAO,QAAQ;AACpC,UAAM,CAAC,WAAW,cAAc,KAAK,0BAA0B;AAC/D,aAAS,KAAK,eAAe;AAC7B,UAAM,MAAM;AAAA,MACV;AAAA,MACA,KAAK,OAAO;AAAA,MACZ;AAAA,MACA,MAAM,SAAS,KAAK,OAAO,cAAc;AAAA,MACzC;AAAA,MACA,KAAK,OAAO;AAAA,MACZ;AAAA,MACA,KAAK;AAEP,WAAO;AAAA;AAAA,EAGT,oBAAoB,OAAO;AAEzB,UAAM,CAAC,WAAW,cAAc,KAAK,0BAA0B;AAC/D,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,OAAO;AAAA,MACZ;AAAA,MACA,MAAM,SAAS,KAAK,OAAO,cAAc;AAAA,MACzC;AAAA,MACA,KAAK;AAEP,WAAO;AAAA;AAAA,EAGT,oBAAoB,WAAW,YAAY;AACzC,WAAO,KAAK,oBAAoB,WAAW;AAAA;AAAA,EAG7C,6BAA6B,OAAO;AAClC,UAAM,CAAC,WAAW,cAAc,KAAK,0BAA0B;AAC/D,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,OAAO;AAAA,MACZ;AAAA,MACA,MAAM,SAAS,KAAK,OAAO,cAAc;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAEP,WAAO;AAAA;AAAA,EAGT,oBAAoB,WAAW,gBAAgB;AAC7C,WAAO,eAAe,KAAK,WAAW,8BAA8B;AAAA;AAAA,EAGtE,uBAAuB,OAAO,SAAS;AACrC,QAAI,QAAQ,QAAQ;AAClB;AAAA;AAGF,YAAQ;AAAA,WACD,YAAY,iBAAiB;AAAA,WAC7B,YAAY,iBAAiB;AAChC,eAAO;AAAA,WACJ,YAAY,iBAAiB;AAGhC,eAAO;AAAA;AAEP,cAAM,IAAI,MAAM,oBAAoB;AAAA;AAAA;AAAA,EAI1C,gBAAgB;AACd,WAAO;AAAA;AAAA,EAGT,sBAAsB,aAAa;AACjC,QAAI,YAAY,QAAQ;AACtB,aAAO,aAAa,KAAK,gBAAgB,YAAY;AAAA;AAGvD,WAAO;AAAA;AAAA,EAGT,uBAAuB,aAAa;AAClC,QAAI,YAAY,QAAQ;AACtB;AAAA;AAGF,WAAO;AAAA;AAAA,EAGT,yBAAyB,aAAa;AACpC,QAAI,YAAY,QAAQ;AACtB,aAAO,yBAAyB,KAAK,gBAAgB,YAAY;AAAA;AAGnE,WAAO;AAAA;AAAA,EAGT,sBAAsB,MAAM,WAAW,SAAS,SAAS,SAAS;AAChE,QAAI;AACJ,QAAI,gBAAgB,MAAM,MAAM;AAE9B,UAAI,KAAK,YAAY;AACnB,cAAM,aAAa,KAAK,qBAAqB,KAAK,YAAY,IAAI,eAChE,GAAG,KAAK,wBAAwB,UAAU,KAAK,IAAI,EAAE,KAAK,UAAU,aAAa,UAAU;AAG7F,eAAO,WAAW,KAAK;AAAA;AAEzB,UAAI,KAAK,MAAM;AAGb,YAAI,KAAK,yBAAyB,KAAK,OAAO;AAC5C,gBAAM,KAAK;AAAA,eACN;AAEL,gBAAM,QAAQ,EAAE,OAAO,KAAK;AAC5B,gBAAM,SAAS,MAAM;AACrB,gBAAM,KAAK,wBAAwB,QAAQ;AAAA;AAE7C,YAAI,KAAK,OAAO;AACd,iBAAO,KAAK,OAAO,SAAS,KAAK,OAAO,KAAK;AAAA;AAG/C,eAAO;AAAA;AAAA;AAGX,QAAI,gBAAgB,MAAM,MAAM;AAC9B,UAAI,KAAK,eAAe,MAAM,iBAAiB;AAC7C,cAAM,KAAK,sBAAsB,KAAK,KAAK,WAAW,SAAS,SAAS;AACxE,YAAI,KAAK,SAAS,WAAW;AAC3B,gBAAM,cAAc;AACpB,iBAAO,QAAQ;AAAA;AACf,YAAI,KAAK,SAAS,iBAAiB,eAAe,KAAK,MAAM;AAC7D,gBAAM,IAAI,MAAM,GAAG;AACnB,iBAAO,GAAG;AAAA;AAAA;AAAA;AAIhB,WAAO,MAAM,sBAAsB,MAAM,WAAW,SAAS,SAAS;AAAA;AAAA,EAGxE,yBAAyB,MAAM;AAC7B,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO;AAAA;AAGT,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AAEtB,WAAO,eAAe,KAAK,QAAQ;AACjC,YAAM,SAAS,KAAK,OAAO;AAC3B,YAAM,kBAAkB,oBAAoB,KAAK;AACjD,UAAI,iBAAiB;AACnB,wBAAgB,gBAAgB,GAAG,QAAQ;AAC3C,0BAAkB;AAClB;AAAA;AAGF,YAAM,kBAAkB,oBAAoB,KAAK;AACjD,UAAI,iBAAiB;AACnB,wBAAgB,gBAAgB,GAAG;AACnC,0BAAkB;AAClB;AAAA;AAGF,YAAM,eAAe,oBAAoB,KAAK;AAC9C,UAAI,cAAc;AAChB,cAAM,gBAAgB,aAAa;AACnC,YAAI,kBAAkB,KAAK;AACzB;AAAA,mBACS,kBAAkB,KAAK;AAChC;AAAA,mBACS,kBAAkB,KAAK;AAChC,4BAAkB;AAClB;AAAA;AAEF,wBAAgB,aAAa,GAAG;AAChC;AAAA;AAGF;AAAA;AAIF,QAAI,mBAAoB,oBAAmB,oBAAoB,kBAAkB;AAC/E,YAAM,IAAI,MAAM,2BAA2B;AAAA;AAI7C,WAAO;AAAA;AAAA,EAGT,wBAAwB,QAAQ,MAAM;AACpC,QAAI,QAAQ,EAAE,OAAO;AACrB,UAAM,eAAe,KAAK,mBAAmB,UAAU,SAAS,KAAK,gBAAgB;AAErF,YAAQ,MAAM,IAAI,aAAW;AAC3B,aAAO,KAAK,KAAK,WAAW,MAAM,SAAS,SAAS,OAAO;AAAA;AAG7D,UAAM,UAAU,KAAK,OAAO,CAAC,KAAK,OAAO,OAAO,KAAK,KAAK,QAAQ,wBAAwB,CAAC,IAAI,UAAU,IAAI;AAE7G,WAAO,cAAc,gBAAgB;AAAA;AAAA,EAGvC,kBAAkB,SAAS,OAAO;AAChC,QAAI,WAAW;AACf,UAAM,SAAS,QAAQ,UAAU,GAC/B,aAAa,QAAQ,mBAAmB,QAAQ,sBAAsB,QAAQ;AAEhF,QAAI,SAAS;AACb,QAAI,QAAQ,OAAO;AACjB,eAAS,KAAK,eAAe,SAAS,OAAO;AAAA;AAG/C,QAAI,QAAQ,SAAS,QAAQ,QAAQ;AAEnC,UAAI,CAAC,OAAO,kBAAkB,CAAC,OAAO,eAAe,UAAU,cAAe,EAAC,OAAO,iBAAiB,CAAC,OAAO,cAAc,SAAS;AACpI,cAAM,kBAAkB,GAAG,KAAK,WAAW,QAAQ,WAAW,MAAM,SAAS,KAAK,gBAAgB,MAAM;AACxG,oBAAY,aAAa;AAAA;AAG3B,UAAI,QAAQ,UAAU,QAAQ,OAAO;AACnC,oBAAY,WAAW,KAAK,OAAO;AAAA;AAGrC,UAAI,QAAQ,OAAO;AACjB,oBAAY,eAAe,KAAK,OAAO,QAAQ;AAAA;AAAA;AAInD,WAAO;AAAA;AAAA,EAGT,aAAa,OAAO;AAClB,WAAO,QAAQ,IAAI;AAAA;AAAA,EAGrB,gBAAgB,YAAY,QAAQ,OAAO;AACzC,UAAM,gBAAgB;AACtB,UAAM,sBAAsB,KAAK,QAAQ,qBAAqB;AAC9D,UAAM,gBAAgB,MAAM,YAAY,YAAY;AACpD,UAAM,SAAS;AAEf,QACE,kBAAkB,QAClB,wBAAwB,SACxB,OAAO,KAAK,kBACZ,CAAC,sBAAsB,SAAS,cAAc,gBAC9C;AAMA,aAAO;AAAA;AAET,WAAO,MAAM,SAAS,eAAe;AAAA;AAAA,EAWvC,UAAU,MAAM,YAAY,GAAG;AAC7B,WAAO,WAAS;AACd,WAAK,KAAK;AACV,aAAO,IAAI,KAAK,SAAS;AAAA;AAAA;AAAA,EAO7B,gBAAgB;AACd,WAAO;AAAA;AAAA;AAKX,8BAA8B,YAAY;AACxC,QAAM,IAAI,MAAM,eAAe;AAAA;", "names": []}