{"version": 3, "sources": ["../../../src/dialects/mssql/index.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst AbstractDialect = require('../abstract');\nconst ConnectionManager = require('./connection-manager');\nconst Query = require('./query');\nconst QueryGenerator = require('./query-generator');\nconst DataTypes = require('../../data-types').mssql;\nconst { MSSqlQueryInterface } = require('./query-interface');\n\nclass MssqlDialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new ConnectionManager(this, sequelize);\n    this.queryGenerator = new QueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n    this.queryInterface = new MSSqlQueryInterface(\n      sequelize,\n      this.queryGenerator\n    );\n  }\n}\n\nMssqlDialect.prototype.supports = _.merge(\n  _.cloneDeep(AbstractDialect.prototype.supports),\n  {\n    DEFAULT: true,\n    'DEFAULT VALUES': true,\n    'LIMIT ON UPDATE': true,\n    'ORDER NULLS': false,\n    lock: false,\n    transactions: true,\n    migrations: false,\n    returnValues: {\n      output: true\n    },\n    schemas: true,\n    autoIncrement: {\n      identityInsert: true,\n      defaultValue: false,\n      update: false\n    },\n    constraints: {\n      restrict: false,\n      default: true\n    },\n    index: {\n      collate: false,\n      length: false,\n      parser: false,\n      type: true,\n      using: false,\n      where: true\n    },\n    NUMERIC: true,\n    tmpTableTrigger: true\n  }\n);\n\nMssqlDialect.prototype.defaultVersion = '12.0.2000'; // SQL Server 2014 Express, minimum supported version\nMssqlDialect.prototype.Query = Query;\nMssqlDialect.prototype.name = 'mssql';\nMssqlDialect.prototype.TICK_CHAR = '\"';\nMssqlDialect.prototype.TICK_CHAR_LEFT = '[';\nMssqlDialect.prototype.TICK_CHAR_RIGHT = ']';\nMssqlDialect.prototype.DataTypes = DataTypes;\n\nmodule.exports = MssqlDialect;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,kBAAkB,QAAQ;AAChC,MAAM,oBAAoB,QAAQ;AAClC,MAAM,QAAQ,QAAQ;AACtB,MAAM,iBAAiB,QAAQ;AAC/B,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,wBAAwB,QAAQ;AAExC,2BAA2B,gBAAgB;AAAA,EACzC,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,kBAAkB,MAAM;AACrD,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACvC,UAAU;AAAA,MACV;AAAA;AAEF,SAAK,iBAAiB,IAAI,oBACxB,WACA,KAAK;AAAA;AAAA;AAKX,aAAa,UAAU,WAAW,EAAE,MAClC,EAAE,UAAU,gBAAgB,UAAU,WACtC;AAAA,EACE,SAAS;AAAA,EACT,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,MAAM;AAAA,EACN,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,IACZ,QAAQ;AAAA;AAAA,EAEV,SAAS;AAAA,EACT,eAAe;AAAA,IACb,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,QAAQ;AAAA;AAAA,EAEV,aAAa;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA;AAAA,EAEX,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA;AAAA,EAET,SAAS;AAAA,EACT,iBAAiB;AAAA;AAIrB,aAAa,UAAU,iBAAiB;AACxC,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,YAAY;AACnC,aAAa,UAAU,iBAAiB;AACxC,aAAa,UAAU,kBAAkB;AACzC,aAAa,UAAU,YAAY;AAEnC,OAAO,UAAU;", "names": []}