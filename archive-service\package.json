{"name": "atma-archive-service", "version": "1.0.0", "description": "Archive Service for ATMA Backend - Stores analysis results", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "dev:setup": "node scripts/start-dev.js", "test:db": "node scripts/test-database.js", "test:api": "node scripts/test-api.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"axios": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-jwt": "^8.4.1", "joi": "^17.9.1", "morgan": "^1.10.0", "pg": "^8.10.0", "sequelize": "^6.31.0", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^8.39.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "sequelize-cli": "^6.6.0", "supertest": "^6.3.3"}, "keywords": ["archive", "storage", "microservices", "express", "nodejs", "postgresql"], "author": "ATMA Team", "license": "MIT"}