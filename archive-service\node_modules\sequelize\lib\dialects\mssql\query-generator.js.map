{"version": 3, "sources": ["../../../src/dialects/mssql/query-generator.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst Utils = require('../../utils');\nconst DataTypes = require('../../data-types');\nconst TableHints = require('../../table-hints');\nconst AbstractQueryGenerator = require('../abstract/query-generator');\nconst randomBytes = require('crypto').randomBytes;\nconst semver = require('semver');\nconst Op = require('../../operators');\n\n/* istanbul ignore next */\nconst throwMethodUndefined = function(methodName) {\n  throw new Error(`The method \"${methodName}\" is not defined! Please add it to your sql dialect.`);\n};\n\nclass MSSQLQueryGenerator extends AbstractQueryGenerator {\n  createDatabaseQuery(databaseName, options) {\n    options = { collate: null, ...options };\n\n    const collation = options.collate ? `COLLATE ${this.escape(options.collate)}` : '';\n\n    return [\n      'IF NOT EXISTS (SELECT * FROM sys.databases WHERE name =', wrapSingleQuote(databaseName), ')',\n      'BEGIN',\n      'CREATE DATABASE', this.quoteIdentifier(databaseName),\n      `${collation};`,\n      'END;'\n    ].join(' ');\n  }\n\n  dropDatabaseQuery(databaseName) {\n    return [\n      'IF EXISTS (SELECT * FROM sys.databases WHERE name =', wrapSingleQuote(databaseName), ')',\n      'BEGIN',\n      'DROP DATABASE', this.quoteIdentifier(databaseName), ';',\n      'END;'\n    ].join(' ');\n  }\n\n  createSchema(schema) {\n    return [\n      'IF NOT EXISTS (SELECT schema_name',\n      'FROM information_schema.schemata',\n      'WHERE schema_name =', wrapSingleQuote(schema), ')',\n      'BEGIN',\n      \"EXEC sp_executesql N'CREATE SCHEMA\",\n      this.quoteIdentifier(schema),\n      \";'\",\n      'END;'\n    ].join(' ');\n  }\n\n  dropSchema(schema) {\n    // Mimics Postgres CASCADE, will drop objects belonging to the schema\n    const quotedSchema = wrapSingleQuote(schema);\n    return [\n      'IF EXISTS (SELECT schema_name',\n      'FROM information_schema.schemata',\n      'WHERE schema_name =', quotedSchema, ')',\n      'BEGIN',\n      'DECLARE @id INT, @ms_sql NVARCHAR(2000);',\n      'DECLARE @cascade TABLE (',\n      'id INT NOT NULL IDENTITY PRIMARY KEY,',\n      'ms_sql NVARCHAR(2000) NOT NULL );',\n      'INSERT INTO @cascade ( ms_sql )',\n      \"SELECT CASE WHEN o.type IN ('F','PK')\",\n      \"THEN N'ALTER TABLE ['+ s.name + N'].[' + p.name + N'] DROP CONSTRAINT [' + o.name + N']'\",\n      \"ELSE N'DROP TABLE ['+ s.name + N'].[' + o.name + N']' END\",\n      'FROM sys.objects o',\n      'JOIN sys.schemas s on o.schema_id = s.schema_id',\n      'LEFT OUTER JOIN sys.objects p on o.parent_object_id = p.object_id',\n      \"WHERE o.type IN ('F', 'PK', 'U') AND s.name = \", quotedSchema,\n      'ORDER BY o.type ASC;',\n      'SELECT TOP 1 @id = id, @ms_sql = ms_sql FROM @cascade ORDER BY id;',\n      'WHILE @id IS NOT NULL',\n      'BEGIN',\n      'BEGIN TRY EXEC sp_executesql @ms_sql; END TRY',\n      'BEGIN CATCH BREAK; THROW; END CATCH;',\n      'DELETE FROM @cascade WHERE id = @id;',\n      'SELECT @id = NULL, @ms_sql = NULL;',\n      'SELECT TOP 1 @id = id, @ms_sql = ms_sql FROM @cascade ORDER BY id;',\n      'END',\n      \"EXEC sp_executesql N'DROP SCHEMA\", this.quoteIdentifier(schema), \";'\",\n      'END;'\n    ].join(' ');\n  }\n\n  showSchemasQuery() {\n    return [\n      'SELECT \"name\" as \"schema_name\" FROM sys.schemas as s',\n      'WHERE \"s\".\"name\" NOT IN (',\n      \"'INFORMATION_SCHEMA', 'dbo', 'guest', 'sys', 'archive'\",\n      ')', 'AND', '\"s\".\"name\" NOT LIKE', \"'db_%'\"\n    ].join(' ');\n  }\n\n  versionQuery() {\n    // Uses string manipulation to convert the MS Maj.Min.Patch.Build to semver Maj.Min.Patch\n    return [\n      'DECLARE @ms_ver NVARCHAR(20);',\n      \"SET @ms_ver = REVERSE(CONVERT(NVARCHAR(20), SERVERPROPERTY('ProductVersion')));\",\n      \"SELECT REVERSE(SUBSTRING(@ms_ver, CHARINDEX('.', @ms_ver)+1, 20)) AS 'version'\"\n    ].join(' ');\n  }\n\n  createTableQuery(tableName, attributes, options) {\n    const primaryKeys = [],\n      foreignKeys = {},\n      attributesClauseParts = [];\n\n    let commentStr = '';\n\n    for (const attr in attributes) {\n      if (Object.prototype.hasOwnProperty.call(attributes, attr)) {\n        let dataType = attributes[attr];\n        let match;\n\n        if (dataType.includes('COMMENT ')) {\n          const commentMatch = dataType.match(/^(.+) (COMMENT.*)$/);\n          const commentText = commentMatch[2].replace('COMMENT', '').trim();\n          commentStr += this.commentTemplate(commentText, tableName, attr);\n          // remove comment related substring from dataType\n          dataType = commentMatch[1];\n        }\n\n        if (dataType.includes('PRIMARY KEY')) {\n          primaryKeys.push(attr);\n\n          if (dataType.includes('REFERENCES')) {\n            // MSSQL doesn't support inline REFERENCES declarations: move to the end\n            match = dataType.match(/^(.+) (REFERENCES.*)$/);\n            attributesClauseParts.push(`${this.quoteIdentifier(attr)} ${match[1].replace('PRIMARY KEY', '')}`);\n            foreignKeys[attr] = match[2];\n          } else {\n            attributesClauseParts.push(`${this.quoteIdentifier(attr)} ${dataType.replace('PRIMARY KEY', '')}`);\n          }\n        } else if (dataType.includes('REFERENCES')) {\n          // MSSQL doesn't support inline REFERENCES declarations: move to the end\n          match = dataType.match(/^(.+) (REFERENCES.*)$/);\n          attributesClauseParts.push(`${this.quoteIdentifier(attr)} ${match[1]}`);\n          foreignKeys[attr] = match[2];\n        } else {\n          attributesClauseParts.push(`${this.quoteIdentifier(attr)} ${dataType}`);\n        }\n      }\n    }\n\n    const pkString = primaryKeys.map(pk => this.quoteIdentifier(pk)).join(', ');\n\n    if (options.uniqueKeys) {\n      _.each(options.uniqueKeys, (columns, indexName) => {\n        if (columns.customIndex) {\n          if (typeof indexName !== 'string') {\n            indexName = `uniq_${tableName}_${columns.fields.join('_')}`;\n          }\n          attributesClauseParts.push(`CONSTRAINT ${\n            this.quoteIdentifier(indexName)\n          } UNIQUE (${\n            columns.fields.map(field => this.quoteIdentifier(field)).join(', ')\n          })`);\n        }\n      });\n    }\n\n    if (pkString.length > 0) {\n      attributesClauseParts.push(`PRIMARY KEY (${pkString})`);\n    }\n\n    for (const fkey in foreignKeys) {\n      if (Object.prototype.hasOwnProperty.call(foreignKeys, fkey)) {\n        attributesClauseParts.push(`FOREIGN KEY (${this.quoteIdentifier(fkey)}) ${foreignKeys[fkey]}`);\n      }\n    }\n\n    const quotedTableName = this.quoteTable(tableName);\n\n    return Utils.joinSQLFragments([\n      `IF OBJECT_ID('${quotedTableName}', 'U') IS NULL`,\n      `CREATE TABLE ${quotedTableName} (${attributesClauseParts.join(', ')})`,\n      ';',\n      commentStr\n    ]);\n  }\n\n  describeTableQuery(tableName, schema) {\n    let sql = [\n      'SELECT',\n      \"c.COLUMN_NAME AS 'Name',\",\n      \"c.DATA_TYPE AS 'Type',\",\n      \"c.CHARACTER_MAXIMUM_LENGTH AS 'Length',\",\n      \"c.IS_NULLABLE as 'IsNull',\",\n      \"COLUMN_DEFAULT AS 'Default',\",\n      \"pk.CONSTRAINT_TYPE AS 'Constraint',\",\n      \"COLUMNPROPERTY(OBJECT_ID('[' + c.TABLE_SCHEMA + '].[' + c.TABLE_NAME + ']'), c.COLUMN_NAME, 'IsIdentity') as 'IsIdentity',\",\n      \"CAST(prop.value AS NVARCHAR) AS 'Comment'\",\n      'FROM',\n      'INFORMATION_SCHEMA.TABLES t',\n      'INNER JOIN',\n      'INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME AND t.TABLE_SCHEMA = c.TABLE_SCHEMA',\n      'LEFT JOIN (SELECT tc.table_schema, tc.table_name, ',\n      'cu.column_name, tc.CONSTRAINT_TYPE ',\n      'FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc ',\n      'JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE  cu ',\n      'ON tc.table_schema=cu.table_schema and tc.table_name=cu.table_name ',\n      'and tc.constraint_name=cu.constraint_name ',\n      'and tc.CONSTRAINT_TYPE=\\'PRIMARY KEY\\') pk ',\n      'ON pk.table_schema=c.table_schema ',\n      'AND pk.table_name=c.table_name ',\n      'AND pk.column_name=c.column_name ',\n      'INNER JOIN sys.columns AS sc',\n      \"ON sc.object_id = OBJECT_ID('[' + t.TABLE_SCHEMA + '].[' + t.TABLE_NAME + ']') AND sc.name = c.column_name\",\n      'LEFT JOIN sys.extended_properties prop ON prop.major_id = sc.object_id',\n      'AND prop.minor_id = sc.column_id',\n      \"AND prop.name = 'MS_Description'\",\n      'WHERE t.TABLE_NAME =', wrapSingleQuote(tableName)\n    ].join(' ');\n\n    if (schema) {\n      sql += `AND t.TABLE_SCHEMA =${wrapSingleQuote(schema)}`;\n    }\n\n    return sql;\n  }\n\n  renameTableQuery(before, after) {\n    return `EXEC sp_rename ${this.quoteTable(before)}, ${this.quoteTable(after)};`;\n  }\n\n  showTablesQuery() {\n    return \"SELECT TABLE_NAME, TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';\";\n  }\n\n  tableExistsQuery(table) {\n    const tableName = table.tableName || table;\n    const schemaName = table.schema || 'dbo';\n\n    return `SELECT TABLE_NAME, TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = ${this.escape(tableName)} AND TABLE_SCHEMA = ${this.escape(schemaName)}`;\n  }\n\n  dropTableQuery(tableName) {\n    const quoteTbl = this.quoteTable(tableName);\n    return Utils.joinSQLFragments([\n      `IF OBJECT_ID('${quoteTbl}', 'U') IS NOT NULL`,\n      'DROP TABLE',\n      quoteTbl,\n      ';'\n    ]);\n  }\n\n  addColumnQuery(table, key, dataType) {\n    // FIXME: attributeToSQL SHOULD be using attributes in addColumnQuery\n    //        but instead we need to pass the key along as the field here\n    dataType.field = key;\n    let commentStr = '';\n\n    if (dataType.comment && _.isString(dataType.comment)) {\n      commentStr = this.commentTemplate(dataType.comment, table, key);\n      // attributeToSQL will try to include `COMMENT 'Comment Text'` when it returns if the comment key\n      // is present. This is needed for createTable statement where that part is extracted with regex.\n      // Here we can intercept the object and remove comment property since we have the original object.\n      delete dataType['comment'];\n    }\n\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(table),\n      'ADD',\n      this.quoteIdentifier(key),\n      this.attributeToSQL(dataType, { context: 'addColumn' }),\n      ';',\n      commentStr\n    ]);\n  }\n\n  commentTemplate(comment, table, column) {\n    return ' EXEC sp_addextendedproperty ' +\n        `@name = N'MS_Description', @value = ${this.escape(comment)}, ` +\n        '@level0type = N\\'Schema\\', @level0name = \\'dbo\\', ' +\n        `@level1type = N'Table', @level1name = ${this.quoteIdentifier(table)}, ` +\n        `@level2type = N'Column', @level2name = ${this.quoteIdentifier(column)};`;\n  }\n\n  removeColumnQuery(tableName, attributeName) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP COLUMN',\n      this.quoteIdentifier(attributeName),\n      ';'\n    ]);\n  }\n\n  changeColumnQuery(tableName, attributes) {\n    const attrString = [],\n      constraintString = [];\n    let commentString = '';\n\n    for (const attributeName in attributes) {\n      const quotedAttrName = this.quoteIdentifier(attributeName);\n      let definition = attributes[attributeName];\n      if (definition.includes('COMMENT ')) {\n        const commentMatch = definition.match(/^(.+) (COMMENT.*)$/);\n        const commentText = commentMatch[2].replace('COMMENT', '').trim();\n        commentString += this.commentTemplate(commentText, tableName, attributeName);\n        // remove comment related substring from dataType\n        definition = commentMatch[1];\n      }\n      if (definition.includes('REFERENCES')) {\n        constraintString.push(`FOREIGN KEY (${quotedAttrName}) ${definition.replace(/.+?(?=REFERENCES)/, '')}`);\n      } else {\n        attrString.push(`${quotedAttrName} ${definition}`);\n      }\n    }\n\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      attrString.length && `ALTER COLUMN ${attrString.join(', ')}`,\n      constraintString.length && `ADD ${constraintString.join(', ')}`,\n      ';',\n      commentString\n    ]);\n  }\n\n  renameColumnQuery(tableName, attrBefore, attributes) {\n    const newName = Object.keys(attributes)[0];\n    return Utils.joinSQLFragments([\n      'EXEC sp_rename',\n      `'${this.quoteTable(tableName)}.${attrBefore}',`,\n      `'${newName}',`,\n      \"'COLUMN'\",\n      ';'\n    ]);\n  }\n\n  bulkInsertQuery(tableName, attrValueHashes, options, attributes) {\n    const quotedTable = this.quoteTable(tableName);\n    options = options || {};\n    attributes = attributes || {};\n\n    const tuples = [];\n    const allAttributes = [];\n    const allQueries = [];\n\n    let needIdentityInsertWrapper = false,\n      outputFragment = '';\n\n    if (options.returning) {\n      const returnValues = this.generateReturnValues(attributes, options);\n\n      outputFragment = returnValues.outputFragment;\n    }\n\n    const emptyQuery = `INSERT INTO ${quotedTable}${outputFragment} DEFAULT VALUES`;\n\n    attrValueHashes.forEach(attrValueHash => {\n      // special case for empty objects with primary keys\n      const fields = Object.keys(attrValueHash);\n      const firstAttr = attributes[fields[0]];\n      if (fields.length === 1 && firstAttr && firstAttr.autoIncrement && attrValueHash[fields[0]] === null) {\n        allQueries.push(emptyQuery);\n        return;\n      }\n\n      // normal case\n      _.forOwn(attrValueHash, (value, key) => {\n        if (value !== null && attributes[key] && attributes[key].autoIncrement) {\n          needIdentityInsertWrapper = true;\n        }\n\n        if (!allAttributes.includes(key)) {\n          if (value === null && attributes[key] && attributes[key].autoIncrement)\n            return;\n\n          allAttributes.push(key);\n        }\n      });\n    });\n\n    if (allAttributes.length > 0) {\n      attrValueHashes.forEach(attrValueHash => {\n        tuples.push(`(${\n          allAttributes.map(key =>\n            this.escape(attrValueHash[key])).join(',')\n        })`);\n      });\n\n      const quotedAttributes = allAttributes.map(attr => this.quoteIdentifier(attr)).join(',');\n      allQueries.push(tupleStr => `INSERT INTO ${quotedTable} (${quotedAttributes})${outputFragment} VALUES ${tupleStr};`);\n    }\n    const commands = [];\n    let offset = 0;\n    const batch = Math.floor(250 / (allAttributes.length + 1)) + 1;\n    while (offset < Math.max(tuples.length, 1)) {\n      const tupleStr = tuples.slice(offset, Math.min(tuples.length, offset + batch));\n      let generatedQuery = allQueries.map(v => typeof v === 'string' ? v : v(tupleStr)).join(';');\n      if (needIdentityInsertWrapper) {\n        generatedQuery = `SET IDENTITY_INSERT ${quotedTable} ON; ${generatedQuery}; SET IDENTITY_INSERT ${quotedTable} OFF;`;\n      }\n      commands.push(generatedQuery);\n      offset += batch;\n    }\n    return commands.join(';');\n  }\n\n  updateQuery(tableName, attrValueHash, where, options, attributes) {\n    const sql = super.updateQuery(tableName, attrValueHash, where, options, attributes);\n    if (options.limit) {\n      const updateArgs = `UPDATE TOP(${this.escape(options.limit)})`;\n      sql.query = sql.query.replace('UPDATE', updateArgs);\n    }\n    return sql;\n  }\n\n  upsertQuery(tableName, insertValues, updateValues, where, model) {\n    const targetTableAlias = this.quoteTable(`${tableName}_target`);\n    const sourceTableAlias = this.quoteTable(`${tableName}_source`);\n    const primaryKeysAttrs = [];\n    const identityAttrs = [];\n    const uniqueAttrs = [];\n    const tableNameQuoted = this.quoteTable(tableName);\n    let needIdentityInsertWrapper = false;\n\n    //Obtain primaryKeys, uniquekeys and identity attrs from rawAttributes as model is not passed\n    for (const key in model.rawAttributes) {\n      if (model.rawAttributes[key].primaryKey) {\n        primaryKeysAttrs.push(model.rawAttributes[key].field || key);\n      }\n      if (model.rawAttributes[key].unique) {\n        uniqueAttrs.push(model.rawAttributes[key].field || key);\n      }\n      if (model.rawAttributes[key].autoIncrement) {\n        identityAttrs.push(model.rawAttributes[key].field || key);\n      }\n    }\n\n    //Add unique indexes defined by indexes option to uniqueAttrs\n    for (const index of model._indexes) {\n      if (index.unique && index.fields) {\n        for (const field of index.fields) {\n          const fieldName = typeof field === 'string' ? field : field.name || field.attribute;\n          if (!uniqueAttrs.includes(fieldName) && model.rawAttributes[fieldName]) {\n            uniqueAttrs.push(fieldName);\n          }\n        }\n      }\n    }\n\n    const updateKeys = Object.keys(updateValues);\n    const insertKeys = Object.keys(insertValues);\n    const insertKeysQuoted = insertKeys.map(key => this.quoteIdentifier(key)).join(', ');\n    const insertValuesEscaped = insertKeys.map(key => this.escape(insertValues[key])).join(', ');\n    const sourceTableQuery = `VALUES(${insertValuesEscaped})`; //Virtual Table\n    let joinCondition;\n\n    //IDENTITY_INSERT Condition\n    identityAttrs.forEach(key => {\n      if (insertValues[key] && insertValues[key] !== null) {\n        needIdentityInsertWrapper = true;\n        /*\n         * IDENTITY_INSERT Column Cannot be updated, only inserted\n         * http://stackoverflow.com/a/30176254/2254360\n         */\n      }\n    });\n\n    //Filter NULL Clauses\n    const clauses = where[Op.or].filter(clause => {\n      let valid = true;\n      /*\n       * Exclude NULL Composite PK/UK. Partial Composite clauses should also be excluded as it doesn't guarantee a single row\n       */\n      for (const key in clause) {\n        if (typeof clause[key] === 'undefined' || clause[key] == null) {\n          valid = false;\n          break;\n        }\n      }\n      return valid;\n    });\n\n    /*\n     * Generate ON condition using PK(s).\n     * If not, generate using UK(s). Else throw error\n     */\n    const getJoinSnippet = array => {\n      return array.map(key => {\n        key = this.quoteIdentifier(key);\n        return `${targetTableAlias}.${key} = ${sourceTableAlias}.${key}`;\n      });\n    };\n\n    if (clauses.length === 0) {\n      throw new Error('Primary Key or Unique key should be passed to upsert query');\n    } else {\n      // Search for primary key attribute in clauses -- Model can have two separate unique keys\n      for (const key in clauses) {\n        const keys = Object.keys(clauses[key]);\n        if (primaryKeysAttrs.includes(keys[0])) {\n          joinCondition = getJoinSnippet(primaryKeysAttrs).join(' AND ');\n          break;\n        }\n      }\n      if (!joinCondition) {\n        joinCondition = getJoinSnippet(uniqueAttrs).join(' AND ');\n      }\n    }\n\n    // Remove the IDENTITY_INSERT Column from update\n    const filteredUpdateClauses = updateKeys.filter(key => !identityAttrs.includes(key))\n      .map(key => {\n        const value = this.escape(updateValues[key]);\n        key = this.quoteIdentifier(key);\n        return `${targetTableAlias}.${key} = ${value}`;\n      });\n    const updateSnippet = filteredUpdateClauses.length > 0 ? `WHEN MATCHED THEN UPDATE SET ${filteredUpdateClauses.join(', ')}` : '';\n\n    const insertSnippet = `(${insertKeysQuoted}) VALUES(${insertValuesEscaped})`;\n\n    let query = `MERGE INTO ${tableNameQuoted} WITH(HOLDLOCK) AS ${targetTableAlias} USING (${sourceTableQuery}) AS ${sourceTableAlias}(${insertKeysQuoted}) ON ${joinCondition}`;\n    query += ` ${updateSnippet} WHEN NOT MATCHED THEN INSERT ${insertSnippet} OUTPUT $action, INSERTED.*;`;\n    if (needIdentityInsertWrapper) {\n      query = `SET IDENTITY_INSERT ${tableNameQuoted} ON; ${query} SET IDENTITY_INSERT ${tableNameQuoted} OFF;`;\n    }\n    return query;\n  }\n\n  truncateTableQuery(tableName) {\n    return `TRUNCATE TABLE ${this.quoteTable(tableName)}`;\n  }\n\n  deleteQuery(tableName, where, options = {}, model) {\n    const table = this.quoteTable(tableName);\n    const whereClause = this.getWhereConditions(where, null, model, options);\n\n    return Utils.joinSQLFragments([\n      'DELETE',\n      options.limit && `TOP(${this.escape(options.limit)})`,\n      'FROM',\n      table,\n      whereClause && `WHERE ${whereClause}`,\n      ';',\n      'SELECT @@ROWCOUNT AS AFFECTEDROWS',\n      ';'\n    ]);\n  }\n\n  showIndexesQuery(tableName) {\n    return `EXEC sys.sp_helpindex @objname = N'${this.quoteTable(tableName)}';`;\n  }\n\n  showConstraintsQuery(tableName) {\n    return `EXEC sp_helpconstraint @objname = ${this.escape(this.quoteTable(tableName))};`;\n  }\n\n  removeIndexQuery(tableName, indexNameOrAttributes) {\n    let indexName = indexNameOrAttributes;\n\n    if (typeof indexName !== 'string') {\n      indexName = Utils.underscore(`${tableName}_${indexNameOrAttributes.join('_')}`);\n    }\n\n    return `DROP INDEX ${this.quoteIdentifiers(indexName)} ON ${this.quoteIdentifiers(tableName)}`;\n  }\n\n  attributeToSQL(attribute, options) {\n    if (!_.isPlainObject(attribute)) {\n      attribute = {\n        type: attribute\n      };\n    }\n\n    // handle self referential constraints\n    if (attribute.references) {\n\n      if (attribute.Model && attribute.Model.tableName === attribute.references.model) {\n        this.sequelize.log('MSSQL does not support self referencial constraints, '\n          + 'we will remove it but we recommend restructuring your query');\n        attribute.onDelete = '';\n        attribute.onUpdate = '';\n      }\n    }\n\n    let template;\n\n    if (attribute.type instanceof DataTypes.ENUM) {\n      if (attribute.type.values && !attribute.values) attribute.values = attribute.type.values;\n\n      // enums are a special case\n      template = attribute.type.toSql();\n      template += ` CHECK (${this.quoteIdentifier(attribute.field)} IN(${attribute.values.map(value => {\n        return this.escape(value);\n      }).join(', ') }))`;\n      return template;\n    }\n    template = attribute.type.toString();\n\n    if (attribute.allowNull === false) {\n      template += ' NOT NULL';\n    } else if (!attribute.primaryKey && !Utils.defaultValueSchemable(attribute.defaultValue)) {\n      template += ' NULL';\n    }\n\n    if (attribute.autoIncrement) {\n      template += ' IDENTITY(1,1)';\n    }\n\n    // Blobs/texts cannot have a defaultValue\n    if (attribute.type !== 'TEXT' && attribute.type._binary !== true &&\n        Utils.defaultValueSchemable(attribute.defaultValue)) {\n      template += ` DEFAULT ${this.escape(attribute.defaultValue)}`;\n    }\n\n    if (attribute.unique === true) {\n      template += ' UNIQUE';\n    }\n\n    if (attribute.primaryKey) {\n      template += ' PRIMARY KEY';\n    }\n\n    if ((!options || !options.withoutForeignKeyConstraints) && attribute.references) {\n      template += ` REFERENCES ${this.quoteTable(attribute.references.model)}`;\n\n      if (attribute.references.key) {\n        template += ` (${this.quoteIdentifier(attribute.references.key)})`;\n      } else {\n        template += ` (${this.quoteIdentifier('id')})`;\n      }\n\n      if (attribute.onDelete) {\n        template += ` ON DELETE ${attribute.onDelete.toUpperCase()}`;\n      }\n\n      if (attribute.onUpdate) {\n        template += ` ON UPDATE ${attribute.onUpdate.toUpperCase()}`;\n      }\n    }\n\n    if (attribute.comment && typeof attribute.comment === 'string') {\n      template += ` COMMENT ${attribute.comment}`;\n    }\n\n    return template;\n  }\n\n  attributesToSQL(attributes, options) {\n    const result = {},\n      existingConstraints = [];\n    let key,\n      attribute;\n\n    for (key in attributes) {\n      attribute = attributes[key];\n\n      if (attribute.references) {\n        if (existingConstraints.includes(attribute.references.model.toString())) {\n          // no cascading constraints to a table more than once\n          attribute.onDelete = '';\n          attribute.onUpdate = '';\n        } else {\n          existingConstraints.push(attribute.references.model.toString());\n\n          // NOTE: this really just disables cascading updates for all\n          //       definitions. Can be made more robust to support the\n          //       few cases where MSSQL actually supports them\n          attribute.onUpdate = '';\n        }\n\n      }\n\n      if (key && !attribute.field) attribute.field = key;\n      result[attribute.field || key] = this.attributeToSQL(attribute, options);\n    }\n\n    return result;\n  }\n\n  createTrigger() {\n    throwMethodUndefined('createTrigger');\n  }\n\n  dropTrigger() {\n    throwMethodUndefined('dropTrigger');\n  }\n\n  renameTrigger() {\n    throwMethodUndefined('renameTrigger');\n  }\n\n  createFunction() {\n    throwMethodUndefined('createFunction');\n  }\n\n  dropFunction() {\n    throwMethodUndefined('dropFunction');\n  }\n\n  renameFunction() {\n    throwMethodUndefined('renameFunction');\n  }\n\n  /**\n   * Generate common SQL prefix for ForeignKeysQuery.\n   *\n   * @param {string} catalogName\n   * @returns {string}\n   */\n  _getForeignKeysQueryPrefix(catalogName) {\n    return `${'SELECT ' +\n        'constraint_name = OBJ.NAME, ' +\n        'constraintName = OBJ.NAME, '}${\n      catalogName ? `constraintCatalog = '${catalogName}', ` : ''\n    }constraintSchema = SCHEMA_NAME(OBJ.SCHEMA_ID), ` +\n        'tableName = TB.NAME, ' +\n        `tableSchema = SCHEMA_NAME(TB.SCHEMA_ID), ${\n          catalogName ? `tableCatalog = '${catalogName}', ` : ''\n        }columnName = COL.NAME, ` +\n        `referencedTableSchema = SCHEMA_NAME(RTB.SCHEMA_ID), ${\n          catalogName ? `referencedCatalog = '${catalogName}', ` : ''\n        }referencedTableName = RTB.NAME, ` +\n        'referencedColumnName = RCOL.NAME ' +\n      'FROM sys.foreign_key_columns FKC ' +\n        'INNER JOIN sys.objects OBJ ON OBJ.OBJECT_ID = FKC.CONSTRAINT_OBJECT_ID ' +\n        'INNER JOIN sys.tables TB ON TB.OBJECT_ID = FKC.PARENT_OBJECT_ID ' +\n        'INNER JOIN sys.columns COL ON COL.COLUMN_ID = PARENT_COLUMN_ID AND COL.OBJECT_ID = TB.OBJECT_ID ' +\n        'INNER JOIN sys.tables RTB ON RTB.OBJECT_ID = FKC.REFERENCED_OBJECT_ID ' +\n        'INNER JOIN sys.columns RCOL ON RCOL.COLUMN_ID = REFERENCED_COLUMN_ID AND RCOL.OBJECT_ID = RTB.OBJECT_ID';\n  }\n\n  /**\n   * Generates an SQL query that returns all foreign keys details of a table.\n   *\n   * @param {string|object} table\n   * @param {string} catalogName database name\n   * @returns {string}\n   */\n  getForeignKeysQuery(table, catalogName) {\n    const tableName = table.tableName || table;\n    let sql = `${this._getForeignKeysQueryPrefix(catalogName)\n    } WHERE TB.NAME =${wrapSingleQuote(tableName)}`;\n\n    if (table.schema) {\n      sql += ` AND SCHEMA_NAME(TB.SCHEMA_ID) =${wrapSingleQuote(table.schema)}`;\n    }\n    return sql;\n  }\n\n  getForeignKeyQuery(table, attributeName) {\n    const tableName = table.tableName || table;\n    return Utils.joinSQLFragments([\n      this._getForeignKeysQueryPrefix(),\n      'WHERE',\n      `TB.NAME =${wrapSingleQuote(tableName)}`,\n      'AND',\n      `COL.NAME =${wrapSingleQuote(attributeName)}`,\n      table.schema && `AND SCHEMA_NAME(TB.SCHEMA_ID) =${wrapSingleQuote(table.schema)}`\n    ]);\n  }\n\n  getPrimaryKeyConstraintQuery(table, attributeName) {\n    const tableName = wrapSingleQuote(table.tableName || table);\n    return Utils.joinSQLFragments([\n      'SELECT K.TABLE_NAME AS tableName,',\n      'K.COLUMN_NAME AS columnName,',\n      'K.CONSTRAINT_NAME AS constraintName',\n      'FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS C',\n      'JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS K',\n      'ON C.TABLE_NAME = K.TABLE_NAME',\n      'AND C.CONSTRAINT_CATALOG = K.CONSTRAINT_CATALOG',\n      'AND C.CONSTRAINT_SCHEMA = K.CONSTRAINT_SCHEMA',\n      'AND C.CONSTRAINT_NAME = K.CONSTRAINT_NAME',\n      'WHERE C.CONSTRAINT_TYPE = \\'PRIMARY KEY\\'',\n      `AND K.COLUMN_NAME = ${wrapSingleQuote(attributeName)}`,\n      `AND K.TABLE_NAME = ${tableName}`,\n      ';'\n    ]);\n  }\n\n  dropForeignKeyQuery(tableName, foreignKey) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP',\n      this.quoteIdentifier(foreignKey)\n    ]);\n  }\n\n  getDefaultConstraintQuery(tableName, attributeName) {\n    const quotedTable = this.quoteTable(tableName);\n    return Utils.joinSQLFragments([\n      'SELECT name FROM sys.default_constraints',\n      `WHERE PARENT_OBJECT_ID = OBJECT_ID('${quotedTable}', 'U')`,\n      `AND PARENT_COLUMN_ID = (SELECT column_id FROM sys.columns WHERE NAME = ('${attributeName}')`,\n      `AND object_id = OBJECT_ID('${quotedTable}', 'U'))`,\n      ';'\n    ]);\n  }\n\n  dropConstraintQuery(tableName, constraintName) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP CONSTRAINT',\n      this.quoteIdentifier(constraintName),\n      ';'\n    ]);\n  }\n\n  setIsolationLevelQuery() {\n\n  }\n\n  generateTransactionId() {\n    return randomBytes(10).toString('hex');\n  }\n\n  startTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return `SAVE TRANSACTION ${this.quoteIdentifier(transaction.name)};`;\n    }\n\n    return 'BEGIN TRANSACTION;';\n  }\n\n  commitTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return;\n    }\n\n    return 'COMMIT TRANSACTION;';\n  }\n\n  rollbackTransactionQuery(transaction) {\n    if (transaction.parent) {\n      return `ROLLBACK TRANSACTION ${this.quoteIdentifier(transaction.name)};`;\n    }\n\n    return 'ROLLBACK TRANSACTION;';\n  }\n\n  selectFromTableFragment(options, model, attributes, tables, mainTableAs, where) {\n    this._throwOnEmptyAttributes(attributes, { modelName: model && model.name, as: mainTableAs });\n\n    const dbVersion = this.sequelize.options.databaseVersion;\n    const isSQLServer2008 = semver.valid(dbVersion) && semver.lt(dbVersion, '11.0.0');\n\n    if (isSQLServer2008 && options.offset) {\n      // For earlier versions of SQL server, we need to nest several queries\n      // in order to emulate the OFFSET behavior.\n      //\n      // 1. The outermost query selects all items from the inner query block.\n      //    This is due to a limitation in SQL server with the use of computed\n      //    columns (e.g. SELECT ROW_NUMBER()...AS x) in WHERE clauses.\n      // 2. The next query handles the LIMIT and OFFSET behavior by getting\n      //    the TOP N rows of the query where the row number is > OFFSET\n      // 3. The innermost query is the actual set we want information from\n\n      const offset = options.offset || 0;\n      const isSubQuery = options.hasIncludeWhere || options.hasIncludeRequired || options.hasMultiAssociation;\n      let orders = { mainQueryOrder: [] };\n      if (options.order) {\n        orders = this.getQueryOrders(options, model, isSubQuery);\n      }\n\n      if (orders.mainQueryOrder.length === 0) {\n        orders.mainQueryOrder.push(this.quoteIdentifier(model.primaryKeyField));\n      }\n\n      const tmpTable = mainTableAs || 'OffsetTable';\n\n      if (options.include) {\n        const subQuery = options.subQuery === undefined ? options.limit && options.hasMultiAssociation : options.subQuery;\n        const mainTable = {\n          name: mainTableAs,\n          quotedName: null,\n          as: null,\n          model\n        };\n        const topLevelInfo = {\n          names: mainTable,\n          options,\n          subQuery\n        };\n\n        let mainJoinQueries = [];\n        for (const include of options.include) {\n          if (include.separate) {\n            continue;\n          }\n          const joinQueries = this.generateInclude(include, { externalAs: mainTableAs, internalAs: mainTableAs }, topLevelInfo);\n          mainJoinQueries = mainJoinQueries.concat(joinQueries.mainQuery);\n        }\n\n        return Utils.joinSQLFragments([\n          'SELECT TOP 100 PERCENT',\n          attributes.join(', '),\n          'FROM (',\n          [\n            'SELECT',\n            options.limit && `TOP ${options.limit}`,\n            '* FROM (',\n            [\n              'SELECT ROW_NUMBER() OVER (',\n              [\n                'ORDER BY',\n                orders.mainQueryOrder.join(', ')\n              ],\n              `) as row_num, ${tmpTable}.* FROM (`,\n              [\n                'SELECT DISTINCT',\n                `${tmpTable}.* FROM ${tables} AS ${tmpTable}`,\n                mainJoinQueries,\n                where && `WHERE ${where}`\n              ],\n              `) AS ${tmpTable}`\n            ],\n            `) AS ${tmpTable} WHERE row_num > ${offset}`\n          ],\n          `) AS ${tmpTable}`\n        ]);\n      }\n      return Utils.joinSQLFragments([\n        'SELECT TOP 100 PERCENT',\n        attributes.join(', '),\n        'FROM (',\n        [\n          'SELECT',\n          options.limit && `TOP ${options.limit}`,\n          '* FROM (',\n          [\n            'SELECT ROW_NUMBER() OVER (',\n            [\n              'ORDER BY',\n              orders.mainQueryOrder.join(', ')\n            ],\n            `) as row_num, * FROM ${tables} AS ${tmpTable}`,\n            where && `WHERE ${where}`\n          ],\n          `) AS ${tmpTable} WHERE row_num > ${offset}`\n        ],\n        `) AS ${tmpTable}`\n      ]);\n    }\n\n    return Utils.joinSQLFragments([\n      'SELECT',\n      isSQLServer2008 && options.limit && `TOP ${options.limit}`,\n      attributes.join(', '),\n      `FROM ${tables}`,\n      mainTableAs && `AS ${mainTableAs}`,\n      options.tableHint && TableHints[options.tableHint] && `WITH (${TableHints[options.tableHint]})`\n    ]);\n  }\n\n  addLimitAndOffset(options, model) {\n    // Skip handling of limit and offset as postfixes for older SQL Server versions\n    if (semver.valid(this.sequelize.options.databaseVersion) && semver.lt(this.sequelize.options.databaseVersion, '11.0.0')) {\n      return '';\n    }\n\n    const offset = options.offset || 0;\n    const isSubQuery = options.subQuery === undefined\n      ? options.hasIncludeWhere || options.hasIncludeRequired || options.hasMultiAssociation\n      : options.subQuery;\n\n    let fragment = '';\n    let orders = {};\n\n    if (options.order) {\n      orders = this.getQueryOrders(options, model, isSubQuery);\n    }\n\n    if (options.limit || options.offset) {\n      // TODO: document why this is adding the primary key of the model in ORDER BY\n      //  if options.include is set\n      if (!options.order || options.order.length === 0 || options.include && orders.subQueryOrder.length === 0) {\n        let primaryKey = model.primaryKeyField;\n\n        const tablePkFragment = `${this.quoteTable(options.tableAs || model.name)}.${this.quoteIdentifier(primaryKey)}`;\n        const aliasedAttribute = (options.attributes || []).find(attr => Array.isArray(attr)\n            && attr[1]\n            && (attr[0] === primaryKey || attr[1] === primaryKey));\n\n        if (aliasedAttribute) {\n          const modelName = this.quoteIdentifier(options.tableAs || model.name);\n          const alias = this._getAliasForField(modelName, aliasedAttribute[1], options);\n\n          primaryKey = new Utils.Col(alias || aliasedAttribute[1]);\n        }\n\n        if (!options.order || !options.order.length) {\n          fragment += ` ORDER BY ${tablePkFragment}`;\n        } else {\n          const orderFieldNames = (options.order || []).map(order => {\n            const value = Array.isArray(order) ? order[0] : order;\n\n            if (value instanceof Utils.Col) {\n              return value.col;\n            }\n\n            if (value instanceof Utils.Literal) {\n              return value.val;\n            }\n\n            return value;\n          });\n          const primaryKeyFieldAlreadyPresent = orderFieldNames.some(\n            fieldName => fieldName === (primaryKey.col || primaryKey)\n          );\n\n          if (!primaryKeyFieldAlreadyPresent) {\n            fragment += options.order && !isSubQuery ? ', ' : ' ORDER BY ';\n            fragment += tablePkFragment;\n          }\n        }\n      }\n\n      if (options.offset || options.limit) {\n        fragment += ` OFFSET ${this.escape(offset)} ROWS`;\n      }\n\n      if (options.limit) {\n        fragment += ` FETCH NEXT ${this.escape(options.limit)} ROWS ONLY`;\n      }\n    }\n\n    return fragment;\n  }\n\n  booleanValue(value) {\n    return value ? 1 : 0;\n  }\n\n  /**\n   * Quote identifier in sql clause\n   *\n   * @param {string} identifier\n   * @param {boolean} force\n   *\n   * @returns {string}\n   */\n  quoteIdentifier(identifier, force) {\n    return `[${identifier.replace(/[[\\]']+/g, '')}]`;\n  }\n}\n\n// private methods\nfunction wrapSingleQuote(identifier) {\n  return Utils.addTicks(Utils.removeTicks(identifier, \"'\"), \"'\");\n}\n\nmodule.exports = MSSQLQueryGenerator;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,QAAQ,QAAQ;AACtB,MAAM,YAAY,QAAQ;AAC1B,MAAM,aAAa,QAAQ;AAC3B,MAAM,yBAAyB,QAAQ;AACvC,MAAM,cAAc,QAAQ,UAAU;AACtC,MAAM,SAAS,QAAQ;AACvB,MAAM,KAAK,QAAQ;AAGnB,MAAM,uBAAuB,SAAS,YAAY;AAChD,QAAM,IAAI,MAAM,eAAe;AAAA;AAGjC,kCAAkC,uBAAuB;AAAA,EACvD,oBAAoB,cAAc,SAAS;AACzC,cAAU,iBAAE,SAAS,QAAS;AAE9B,UAAM,YAAY,QAAQ,UAAU,WAAW,KAAK,OAAO,QAAQ,aAAa;AAEhF,WAAO;AAAA,MACL;AAAA,MAA2D,gBAAgB;AAAA,MAAe;AAAA,MAC1F;AAAA,MACA;AAAA,MAAmB,KAAK,gBAAgB;AAAA,MACxC,GAAG;AAAA,MACH;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,kBAAkB,cAAc;AAC9B,WAAO;AAAA,MACL;AAAA,MAAuD,gBAAgB;AAAA,MAAe;AAAA,MACtF;AAAA,MACA;AAAA,MAAiB,KAAK,gBAAgB;AAAA,MAAe;AAAA,MACrD;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,aAAa,QAAQ;AACnB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MAAuB,gBAAgB;AAAA,MAAS;AAAA,MAChD;AAAA,MACA;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,WAAW,QAAQ;AAEjB,UAAM,eAAe,gBAAgB;AACrC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MAAuB;AAAA,MAAc;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAAkD;AAAA,MAClD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAAoC,KAAK,gBAAgB;AAAA,MAAS;AAAA,MAClE;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,mBAAmB;AACjB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAAK;AAAA,MAAO;AAAA,MAAuB;AAAA,MACnC,KAAK;AAAA;AAAA,EAGT,eAAe;AAEb,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA;AAAA,EAGT,iBAAiB,WAAW,YAAY,SAAS;AAC/C,UAAM,cAAc,IAClB,cAAc,IACd,wBAAwB;AAE1B,QAAI,aAAa;AAEjB,eAAW,QAAQ,YAAY;AAC7B,UAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO;AAC1D,YAAI,WAAW,WAAW;AAC1B,YAAI;AAEJ,YAAI,SAAS,SAAS,aAAa;AACjC,gBAAM,eAAe,SAAS,MAAM;AACpC,gBAAM,cAAc,aAAa,GAAG,QAAQ,WAAW,IAAI;AAC3D,wBAAc,KAAK,gBAAgB,aAAa,WAAW;AAE3D,qBAAW,aAAa;AAAA;AAG1B,YAAI,SAAS,SAAS,gBAAgB;AACpC,sBAAY,KAAK;AAEjB,cAAI,SAAS,SAAS,eAAe;AAEnC,oBAAQ,SAAS,MAAM;AACvB,kCAAsB,KAAK,GAAG,KAAK,gBAAgB,SAAS,MAAM,GAAG,QAAQ,eAAe;AAC5F,wBAAY,QAAQ,MAAM;AAAA,iBACrB;AACL,kCAAsB,KAAK,GAAG,KAAK,gBAAgB,SAAS,SAAS,QAAQ,eAAe;AAAA;AAAA,mBAErF,SAAS,SAAS,eAAe;AAE1C,kBAAQ,SAAS,MAAM;AACvB,gCAAsB,KAAK,GAAG,KAAK,gBAAgB,SAAS,MAAM;AAClE,sBAAY,QAAQ,MAAM;AAAA,eACrB;AACL,gCAAsB,KAAK,GAAG,KAAK,gBAAgB,SAAS;AAAA;AAAA;AAAA;AAKlE,UAAM,WAAW,YAAY,IAAI,QAAM,KAAK,gBAAgB,KAAK,KAAK;AAEtE,QAAI,QAAQ,YAAY;AACtB,QAAE,KAAK,QAAQ,YAAY,CAAC,SAAS,cAAc;AACjD,YAAI,QAAQ,aAAa;AACvB,cAAI,OAAO,cAAc,UAAU;AACjC,wBAAY,QAAQ,aAAa,QAAQ,OAAO,KAAK;AAAA;AAEvD,gCAAsB,KAAK,cACzB,KAAK,gBAAgB,sBAErB,QAAQ,OAAO,IAAI,WAAS,KAAK,gBAAgB,QAAQ,KAAK;AAAA;AAAA;AAAA;AAMtE,QAAI,SAAS,SAAS,GAAG;AACvB,4BAAsB,KAAK,gBAAgB;AAAA;AAG7C,eAAW,QAAQ,aAAa;AAC9B,UAAI,OAAO,UAAU,eAAe,KAAK,aAAa,OAAO;AAC3D,8BAAsB,KAAK,gBAAgB,KAAK,gBAAgB,UAAU,YAAY;AAAA;AAAA;AAI1F,UAAM,kBAAkB,KAAK,WAAW;AAExC,WAAO,MAAM,iBAAiB;AAAA,MAC5B,iBAAiB;AAAA,MACjB,gBAAgB,oBAAoB,sBAAsB,KAAK;AAAA,MAC/D;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,mBAAmB,WAAW,QAAQ;AACpC,QAAI,MAAM;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAAwB,gBAAgB;AAAA,MACxC,KAAK;AAEP,QAAI,QAAQ;AACV,aAAO,uBAAuB,gBAAgB;AAAA;AAGhD,WAAO;AAAA;AAAA,EAGT,iBAAiB,QAAQ,OAAO;AAC9B,WAAO,kBAAkB,KAAK,WAAW,YAAY,KAAK,WAAW;AAAA;AAAA,EAGvE,kBAAkB;AAChB,WAAO;AAAA;AAAA,EAGT,iBAAiB,OAAO;AACtB,UAAM,YAAY,MAAM,aAAa;AACrC,UAAM,aAAa,MAAM,UAAU;AAEnC,WAAO,mHAAmH,KAAK,OAAO,iCAAiC,KAAK,OAAO;AAAA;AAAA,EAGrL,eAAe,WAAW;AACxB,UAAM,WAAW,KAAK,WAAW;AACjC,WAAO,MAAM,iBAAiB;AAAA,MAC5B,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,eAAe,OAAO,KAAK,UAAU;AAGnC,aAAS,QAAQ;AACjB,QAAI,aAAa;AAEjB,QAAI,SAAS,WAAW,EAAE,SAAS,SAAS,UAAU;AACpD,mBAAa,KAAK,gBAAgB,SAAS,SAAS,OAAO;AAI3D,aAAO,SAAS;AAAA;AAGlB,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK,eAAe,UAAU,EAAE,SAAS;AAAA,MACzC;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,gBAAgB,SAAS,OAAO,QAAQ;AACtC,WAAO,oEACoC,KAAK,OAAO,iGAEV,KAAK,gBAAgB,kDACpB,KAAK,gBAAgB;AAAA;AAAA,EAGrE,kBAAkB,WAAW,eAAe;AAC1C,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,YAAY;AACvC,UAAM,aAAa,IACjB,mBAAmB;AACrB,QAAI,gBAAgB;AAEpB,eAAW,iBAAiB,YAAY;AACtC,YAAM,iBAAiB,KAAK,gBAAgB;AAC5C,UAAI,aAAa,WAAW;AAC5B,UAAI,WAAW,SAAS,aAAa;AACnC,cAAM,eAAe,WAAW,MAAM;AACtC,cAAM,cAAc,aAAa,GAAG,QAAQ,WAAW,IAAI;AAC3D,yBAAiB,KAAK,gBAAgB,aAAa,WAAW;AAE9D,qBAAa,aAAa;AAAA;AAE5B,UAAI,WAAW,SAAS,eAAe;AACrC,yBAAiB,KAAK,gBAAgB,mBAAmB,WAAW,QAAQ,qBAAqB;AAAA,aAC5F;AACL,mBAAW,KAAK,GAAG,kBAAkB;AAAA;AAAA;AAIzC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB,WAAW,UAAU,gBAAgB,WAAW,KAAK;AAAA,MACrD,iBAAiB,UAAU,OAAO,iBAAiB,KAAK;AAAA,MACxD;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,YAAY,YAAY;AACnD,UAAM,UAAU,OAAO,KAAK,YAAY;AACxC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,IAAI,KAAK,WAAW,cAAc;AAAA,MAClC,IAAI;AAAA,MACJ;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,gBAAgB,WAAW,iBAAiB,SAAS,YAAY;AAC/D,UAAM,cAAc,KAAK,WAAW;AACpC,cAAU,WAAW;AACrB,iBAAa,cAAc;AAE3B,UAAM,SAAS;AACf,UAAM,gBAAgB;AACtB,UAAM,aAAa;AAEnB,QAAI,4BAA4B,OAC9B,iBAAiB;AAEnB,QAAI,QAAQ,WAAW;AACrB,YAAM,eAAe,KAAK,qBAAqB,YAAY;AAE3D,uBAAiB,aAAa;AAAA;AAGhC,UAAM,aAAa,eAAe,cAAc;AAEhD,oBAAgB,QAAQ,mBAAiB;AAEvC,YAAM,SAAS,OAAO,KAAK;AAC3B,YAAM,YAAY,WAAW,OAAO;AACpC,UAAI,OAAO,WAAW,KAAK,aAAa,UAAU,iBAAiB,cAAc,OAAO,QAAQ,MAAM;AACpG,mBAAW,KAAK;AAChB;AAAA;AAIF,QAAE,OAAO,eAAe,CAAC,OAAO,QAAQ;AACtC,YAAI,UAAU,QAAQ,WAAW,QAAQ,WAAW,KAAK,eAAe;AACtE,sCAA4B;AAAA;AAG9B,YAAI,CAAC,cAAc,SAAS,MAAM;AAChC,cAAI,UAAU,QAAQ,WAAW,QAAQ,WAAW,KAAK;AACvD;AAEF,wBAAc,KAAK;AAAA;AAAA;AAAA;AAKzB,QAAI,cAAc,SAAS,GAAG;AAC5B,sBAAgB,QAAQ,mBAAiB;AACvC,eAAO,KAAK,IACV,cAAc,IAAI,SAChB,KAAK,OAAO,cAAc,OAAO,KAAK;AAAA;AAI5C,YAAM,mBAAmB,cAAc,IAAI,UAAQ,KAAK,gBAAgB,OAAO,KAAK;AACpF,iBAAW,KAAK,cAAY,eAAe,gBAAgB,oBAAoB,yBAAyB;AAAA;AAE1G,UAAM,WAAW;AACjB,QAAI,SAAS;AACb,UAAM,QAAQ,KAAK,MAAM,MAAO,eAAc,SAAS,MAAM;AAC7D,WAAO,SAAS,KAAK,IAAI,OAAO,QAAQ,IAAI;AAC1C,YAAM,WAAW,OAAO,MAAM,QAAQ,KAAK,IAAI,OAAO,QAAQ,SAAS;AACvE,UAAI,iBAAiB,WAAW,IAAI,OAAK,OAAO,MAAM,WAAW,IAAI,EAAE,WAAW,KAAK;AACvF,UAAI,2BAA2B;AAC7B,yBAAiB,uBAAuB,mBAAmB,uCAAuC;AAAA;AAEpG,eAAS,KAAK;AACd,gBAAU;AAAA;AAEZ,WAAO,SAAS,KAAK;AAAA;AAAA,EAGvB,YAAY,WAAW,eAAe,OAAO,SAAS,YAAY;AAChE,UAAM,MAAM,MAAM,YAAY,WAAW,eAAe,OAAO,SAAS;AACxE,QAAI,QAAQ,OAAO;AACjB,YAAM,aAAa,cAAc,KAAK,OAAO,QAAQ;AACrD,UAAI,QAAQ,IAAI,MAAM,QAAQ,UAAU;AAAA;AAE1C,WAAO;AAAA;AAAA,EAGT,YAAY,WAAW,cAAc,cAAc,OAAO,OAAO;AAC/D,UAAM,mBAAmB,KAAK,WAAW,GAAG;AAC5C,UAAM,mBAAmB,KAAK,WAAW,GAAG;AAC5C,UAAM,mBAAmB;AACzB,UAAM,gBAAgB;AACtB,UAAM,cAAc;AACpB,UAAM,kBAAkB,KAAK,WAAW;AACxC,QAAI,4BAA4B;AAGhC,eAAW,OAAO,MAAM,eAAe;AACrC,UAAI,MAAM,cAAc,KAAK,YAAY;AACvC,yBAAiB,KAAK,MAAM,cAAc,KAAK,SAAS;AAAA;AAE1D,UAAI,MAAM,cAAc,KAAK,QAAQ;AACnC,oBAAY,KAAK,MAAM,cAAc,KAAK,SAAS;AAAA;AAErD,UAAI,MAAM,cAAc,KAAK,eAAe;AAC1C,sBAAc,KAAK,MAAM,cAAc,KAAK,SAAS;AAAA;AAAA;AAKzD,eAAW,SAAS,MAAM,UAAU;AAClC,UAAI,MAAM,UAAU,MAAM,QAAQ;AAChC,mBAAW,SAAS,MAAM,QAAQ;AAChC,gBAAM,YAAY,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ,MAAM;AAC1E,cAAI,CAAC,YAAY,SAAS,cAAc,MAAM,cAAc,YAAY;AACtE,wBAAY,KAAK;AAAA;AAAA;AAAA;AAAA;AAMzB,UAAM,aAAa,OAAO,KAAK;AAC/B,UAAM,aAAa,OAAO,KAAK;AAC/B,UAAM,mBAAmB,WAAW,IAAI,SAAO,KAAK,gBAAgB,MAAM,KAAK;AAC/E,UAAM,sBAAsB,WAAW,IAAI,SAAO,KAAK,OAAO,aAAa,OAAO,KAAK;AACvF,UAAM,mBAAmB,UAAU;AACnC,QAAI;AAGJ,kBAAc,QAAQ,SAAO;AAC3B,UAAI,aAAa,QAAQ,aAAa,SAAS,MAAM;AACnD,oCAA4B;AAAA;AAAA;AAShC,UAAM,UAAU,MAAM,GAAG,IAAI,OAAO,YAAU;AAC5C,UAAI,QAAQ;AAIZ,iBAAW,OAAO,QAAQ;AACxB,YAAI,OAAO,OAAO,SAAS,eAAe,OAAO,QAAQ,MAAM;AAC7D,kBAAQ;AACR;AAAA;AAAA;AAGJ,aAAO;AAAA;AAOT,UAAM,iBAAiB,WAAS;AAC9B,aAAO,MAAM,IAAI,SAAO;AACtB,cAAM,KAAK,gBAAgB;AAC3B,eAAO,GAAG,oBAAoB,SAAS,oBAAoB;AAAA;AAAA;AAI/D,QAAI,QAAQ,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM;AAAA,WACX;AAEL,iBAAW,OAAO,SAAS;AACzB,cAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,YAAI,iBAAiB,SAAS,KAAK,KAAK;AACtC,0BAAgB,eAAe,kBAAkB,KAAK;AACtD;AAAA;AAAA;AAGJ,UAAI,CAAC,eAAe;AAClB,wBAAgB,eAAe,aAAa,KAAK;AAAA;AAAA;AAKrD,UAAM,wBAAwB,WAAW,OAAO,SAAO,CAAC,cAAc,SAAS,MAC5E,IAAI,SAAO;AACV,YAAM,QAAQ,KAAK,OAAO,aAAa;AACvC,YAAM,KAAK,gBAAgB;AAC3B,aAAO,GAAG,oBAAoB,SAAS;AAAA;AAE3C,UAAM,gBAAgB,sBAAsB,SAAS,IAAI,gCAAgC,sBAAsB,KAAK,UAAU;AAE9H,UAAM,gBAAgB,IAAI,4BAA4B;AAEtD,QAAI,QAAQ,cAAc,qCAAqC,2BAA2B,wBAAwB,oBAAoB,wBAAwB;AAC9J,aAAS,IAAI,8CAA8C;AAC3D,QAAI,2BAA2B;AAC7B,cAAQ,uBAAuB,uBAAuB,6BAA6B;AAAA;AAErF,WAAO;AAAA;AAAA,EAGT,mBAAmB,WAAW;AAC5B,WAAO,kBAAkB,KAAK,WAAW;AAAA;AAAA,EAG3C,YAAY,WAAW,OAAO,UAAU,IAAI,OAAO;AACjD,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,cAAc,KAAK,mBAAmB,OAAO,MAAM,OAAO;AAEhE,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,QAAQ,SAAS,OAAO,KAAK,OAAO,QAAQ;AAAA,MAC5C;AAAA,MACA;AAAA,MACA,eAAe,SAAS;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,iBAAiB,WAAW;AAC1B,WAAO,sCAAsC,KAAK,WAAW;AAAA;AAAA,EAG/D,qBAAqB,WAAW;AAC9B,WAAO,qCAAqC,KAAK,OAAO,KAAK,WAAW;AAAA;AAAA,EAG1E,iBAAiB,WAAW,uBAAuB;AACjD,QAAI,YAAY;AAEhB,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,MAAM,WAAW,GAAG,aAAa,sBAAsB,KAAK;AAAA;AAG1E,WAAO,cAAc,KAAK,iBAAiB,iBAAiB,KAAK,iBAAiB;AAAA;AAAA,EAGpF,eAAe,WAAW,SAAS;AACjC,QAAI,CAAC,EAAE,cAAc,YAAY;AAC/B,kBAAY;AAAA,QACV,MAAM;AAAA;AAAA;AAKV,QAAI,UAAU,YAAY;AAExB,UAAI,UAAU,SAAS,UAAU,MAAM,cAAc,UAAU,WAAW,OAAO;AAC/E,aAAK,UAAU,IAAI;AAEnB,kBAAU,WAAW;AACrB,kBAAU,WAAW;AAAA;AAAA;AAIzB,QAAI;AAEJ,QAAI,UAAU,gBAAgB,UAAU,MAAM;AAC5C,UAAI,UAAU,KAAK,UAAU,CAAC,UAAU;AAAQ,kBAAU,SAAS,UAAU,KAAK;AAGlF,iBAAW,UAAU,KAAK;AAC1B,kBAAY,WAAW,KAAK,gBAAgB,UAAU,aAAa,UAAU,OAAO,IAAI,WAAS;AAC/F,eAAO,KAAK,OAAO;AAAA,SAClB,KAAK;AACR,aAAO;AAAA;AAET,eAAW,UAAU,KAAK;AAE1B,QAAI,UAAU,cAAc,OAAO;AACjC,kBAAY;AAAA,eACH,CAAC,UAAU,cAAc,CAAC,MAAM,sBAAsB,UAAU,eAAe;AACxF,kBAAY;AAAA;AAGd,QAAI,UAAU,eAAe;AAC3B,kBAAY;AAAA;AAId,QAAI,UAAU,SAAS,UAAU,UAAU,KAAK,YAAY,QACxD,MAAM,sBAAsB,UAAU,eAAe;AACvD,kBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAGhD,QAAI,UAAU,WAAW,MAAM;AAC7B,kBAAY;AAAA;AAGd,QAAI,UAAU,YAAY;AACxB,kBAAY;AAAA;AAGd,QAAK,EAAC,WAAW,CAAC,QAAQ,iCAAiC,UAAU,YAAY;AAC/E,kBAAY,eAAe,KAAK,WAAW,UAAU,WAAW;AAEhE,UAAI,UAAU,WAAW,KAAK;AAC5B,oBAAY,KAAK,KAAK,gBAAgB,UAAU,WAAW;AAAA,aACtD;AACL,oBAAY,KAAK,KAAK,gBAAgB;AAAA;AAGxC,UAAI,UAAU,UAAU;AACtB,oBAAY,cAAc,UAAU,SAAS;AAAA;AAG/C,UAAI,UAAU,UAAU;AACtB,oBAAY,cAAc,UAAU,SAAS;AAAA;AAAA;AAIjD,QAAI,UAAU,WAAW,OAAO,UAAU,YAAY,UAAU;AAC9D,kBAAY,YAAY,UAAU;AAAA;AAGpC,WAAO;AAAA;AAAA,EAGT,gBAAgB,YAAY,SAAS;AACnC,UAAM,SAAS,IACb,sBAAsB;AACxB,QAAI,KACF;AAEF,SAAK,OAAO,YAAY;AACtB,kBAAY,WAAW;AAEvB,UAAI,UAAU,YAAY;AACxB,YAAI,oBAAoB,SAAS,UAAU,WAAW,MAAM,aAAa;AAEvE,oBAAU,WAAW;AACrB,oBAAU,WAAW;AAAA,eAChB;AACL,8BAAoB,KAAK,UAAU,WAAW,MAAM;AAKpD,oBAAU,WAAW;AAAA;AAAA;AAKzB,UAAI,OAAO,CAAC,UAAU;AAAO,kBAAU,QAAQ;AAC/C,aAAO,UAAU,SAAS,OAAO,KAAK,eAAe,WAAW;AAAA;AAGlE,WAAO;AAAA;AAAA,EAGT,gBAAgB;AACd,yBAAqB;AAAA;AAAA,EAGvB,cAAc;AACZ,yBAAqB;AAAA;AAAA,EAGvB,gBAAgB;AACd,yBAAqB;AAAA;AAAA,EAGvB,iBAAiB;AACf,yBAAqB;AAAA;AAAA,EAGvB,eAAe;AACb,yBAAqB;AAAA;AAAA,EAGvB,iBAAiB;AACf,yBAAqB;AAAA;AAAA,EASvB,2BAA2B,aAAa;AACtC,WAAO,GAAG,mEAGR,cAAc,wBAAwB,mBAAmB,kHAIrD,cAAc,mBAAmB,mBAAmB,gFAGpD,cAAc,wBAAwB,mBAAmB;AAAA;AAAA,EAkBjE,oBAAoB,OAAO,aAAa;AACtC,UAAM,YAAY,MAAM,aAAa;AACrC,QAAI,MAAM,GAAG,KAAK,2BAA2B,+BAC1B,gBAAgB;AAEnC,QAAI,MAAM,QAAQ;AAChB,aAAO,mCAAmC,gBAAgB,MAAM;AAAA;AAElE,WAAO;AAAA;AAAA,EAGT,mBAAmB,OAAO,eAAe;AACvC,UAAM,YAAY,MAAM,aAAa;AACrC,WAAO,MAAM,iBAAiB;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACA,YAAY,gBAAgB;AAAA,MAC5B;AAAA,MACA,aAAa,gBAAgB;AAAA,MAC7B,MAAM,UAAU,kCAAkC,gBAAgB,MAAM;AAAA;AAAA;AAAA,EAI5E,6BAA6B,OAAO,eAAe;AACjD,UAAM,YAAY,gBAAgB,MAAM,aAAa;AACrD,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,uBAAuB,gBAAgB;AAAA,MACvC,sBAAsB;AAAA,MACtB;AAAA;AAAA;AAAA,EAIJ,oBAAoB,WAAW,YAAY;AACzC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA;AAAA;AAAA,EAIzB,0BAA0B,WAAW,eAAe;AAClD,UAAM,cAAc,KAAK,WAAW;AACpC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,uCAAuC;AAAA,MACvC,4EAA4E;AAAA,MAC5E,8BAA8B;AAAA,MAC9B;AAAA;AAAA;AAAA,EAIJ,oBAAoB,WAAW,gBAAgB;AAC7C,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAAA;AAAA,EAIJ,yBAAyB;AAAA;AAAA,EAIzB,wBAAwB;AACtB,WAAO,YAAY,IAAI,SAAS;AAAA;AAAA,EAGlC,sBAAsB,aAAa;AACjC,QAAI,YAAY,QAAQ;AACtB,aAAO,oBAAoB,KAAK,gBAAgB,YAAY;AAAA;AAG9D,WAAO;AAAA;AAAA,EAGT,uBAAuB,aAAa;AAClC,QAAI,YAAY,QAAQ;AACtB;AAAA;AAGF,WAAO;AAAA;AAAA,EAGT,yBAAyB,aAAa;AACpC,QAAI,YAAY,QAAQ;AACtB,aAAO,wBAAwB,KAAK,gBAAgB,YAAY;AAAA;AAGlE,WAAO;AAAA;AAAA,EAGT,wBAAwB,SAAS,OAAO,YAAY,QAAQ,aAAa,OAAO;AAC9E,SAAK,wBAAwB,YAAY,EAAE,WAAW,SAAS,MAAM,MAAM,IAAI;AAE/E,UAAM,YAAY,KAAK,UAAU,QAAQ;AACzC,UAAM,kBAAkB,OAAO,MAAM,cAAc,OAAO,GAAG,WAAW;AAExE,QAAI,mBAAmB,QAAQ,QAAQ;AAWrC,YAAM,SAAS,QAAQ,UAAU;AACjC,YAAM,aAAa,QAAQ,mBAAmB,QAAQ,sBAAsB,QAAQ;AACpF,UAAI,SAAS,EAAE,gBAAgB;AAC/B,UAAI,QAAQ,OAAO;AACjB,iBAAS,KAAK,eAAe,SAAS,OAAO;AAAA;AAG/C,UAAI,OAAO,eAAe,WAAW,GAAG;AACtC,eAAO,eAAe,KAAK,KAAK,gBAAgB,MAAM;AAAA;AAGxD,YAAM,WAAW,eAAe;AAEhC,UAAI,QAAQ,SAAS;AACnB,cAAM,WAAW,QAAQ,aAAa,SAAY,QAAQ,SAAS,QAAQ,sBAAsB,QAAQ;AACzG,cAAM,YAAY;AAAA,UAChB,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,IAAI;AAAA,UACJ;AAAA;AAEF,cAAM,eAAe;AAAA,UACnB,OAAO;AAAA,UACP;AAAA,UACA;AAAA;AAGF,YAAI,kBAAkB;AACtB,mBAAW,WAAW,QAAQ,SAAS;AACrC,cAAI,QAAQ,UAAU;AACpB;AAAA;AAEF,gBAAM,cAAc,KAAK,gBAAgB,SAAS,EAAE,YAAY,aAAa,YAAY,eAAe;AACxG,4BAAkB,gBAAgB,OAAO,YAAY;AAAA;AAGvD,eAAO,MAAM,iBAAiB;AAAA,UAC5B;AAAA,UACA,WAAW,KAAK;AAAA,UAChB;AAAA,UACA;AAAA,YACE;AAAA,YACA,QAAQ,SAAS,OAAO,QAAQ;AAAA,YAChC;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,gBACE;AAAA,gBACA,OAAO,eAAe,KAAK;AAAA;AAAA,cAE7B,iBAAiB;AAAA,cACjB;AAAA,gBACE;AAAA,gBACA,GAAG,mBAAmB,aAAa;AAAA,gBACnC;AAAA,gBACA,SAAS,SAAS;AAAA;AAAA,cAEpB,QAAQ;AAAA;AAAA,YAEV,QAAQ,4BAA4B;AAAA;AAAA,UAEtC,QAAQ;AAAA;AAAA;AAGZ,aAAO,MAAM,iBAAiB;AAAA,QAC5B;AAAA,QACA,WAAW,KAAK;AAAA,QAChB;AAAA,QACA;AAAA,UACE;AAAA,UACA,QAAQ,SAAS,OAAO,QAAQ;AAAA,UAChC;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA,cACE;AAAA,cACA,OAAO,eAAe,KAAK;AAAA;AAAA,YAE7B,wBAAwB,aAAa;AAAA,YACrC,SAAS,SAAS;AAAA;AAAA,UAEpB,QAAQ,4BAA4B;AAAA;AAAA,QAEtC,QAAQ;AAAA;AAAA;AAIZ,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,mBAAmB,QAAQ,SAAS,OAAO,QAAQ;AAAA,MACnD,WAAW,KAAK;AAAA,MAChB,QAAQ;AAAA,MACR,eAAe,MAAM;AAAA,MACrB,QAAQ,aAAa,WAAW,QAAQ,cAAc,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA,EAItF,kBAAkB,SAAS,OAAO;AAEhC,QAAI,OAAO,MAAM,KAAK,UAAU,QAAQ,oBAAoB,OAAO,GAAG,KAAK,UAAU,QAAQ,iBAAiB,WAAW;AACvH,aAAO;AAAA;AAGT,UAAM,SAAS,QAAQ,UAAU;AACjC,UAAM,aAAa,QAAQ,aAAa,SACpC,QAAQ,mBAAmB,QAAQ,sBAAsB,QAAQ,sBACjE,QAAQ;AAEZ,QAAI,WAAW;AACf,QAAI,SAAS;AAEb,QAAI,QAAQ,OAAO;AACjB,eAAS,KAAK,eAAe,SAAS,OAAO;AAAA;AAG/C,QAAI,QAAQ,SAAS,QAAQ,QAAQ;AAGnC,UAAI,CAAC,QAAQ,SAAS,QAAQ,MAAM,WAAW,KAAK,QAAQ,WAAW,OAAO,cAAc,WAAW,GAAG;AACxG,YAAI,aAAa,MAAM;AAEvB,cAAM,kBAAkB,GAAG,KAAK,WAAW,QAAQ,WAAW,MAAM,SAAS,KAAK,gBAAgB;AAClG,cAAM,mBAAoB,SAAQ,cAAc,IAAI,KAAK,UAAQ,MAAM,QAAQ,SACxE,KAAK,MACJ,MAAK,OAAO,cAAc,KAAK,OAAO;AAE9C,YAAI,kBAAkB;AACpB,gBAAM,YAAY,KAAK,gBAAgB,QAAQ,WAAW,MAAM;AAChE,gBAAM,QAAQ,KAAK,kBAAkB,WAAW,iBAAiB,IAAI;AAErE,uBAAa,IAAI,MAAM,IAAI,SAAS,iBAAiB;AAAA;AAGvD,YAAI,CAAC,QAAQ,SAAS,CAAC,QAAQ,MAAM,QAAQ;AAC3C,sBAAY,aAAa;AAAA,eACpB;AACL,gBAAM,kBAAmB,SAAQ,SAAS,IAAI,IAAI,WAAS;AACzD,kBAAM,QAAQ,MAAM,QAAQ,SAAS,MAAM,KAAK;AAEhD,gBAAI,iBAAiB,MAAM,KAAK;AAC9B,qBAAO,MAAM;AAAA;AAGf,gBAAI,iBAAiB,MAAM,SAAS;AAClC,qBAAO,MAAM;AAAA;AAGf,mBAAO;AAAA;AAET,gBAAM,gCAAgC,gBAAgB,KACpD,eAAa,cAAe,YAAW,OAAO;AAGhD,cAAI,CAAC,+BAA+B;AAClC,wBAAY,QAAQ,SAAS,CAAC,aAAa,OAAO;AAClD,wBAAY;AAAA;AAAA;AAAA;AAKlB,UAAI,QAAQ,UAAU,QAAQ,OAAO;AACnC,oBAAY,WAAW,KAAK,OAAO;AAAA;AAGrC,UAAI,QAAQ,OAAO;AACjB,oBAAY,eAAe,KAAK,OAAO,QAAQ;AAAA;AAAA;AAInD,WAAO;AAAA;AAAA,EAGT,aAAa,OAAO;AAClB,WAAO,QAAQ,IAAI;AAAA;AAAA,EAWrB,gBAAgB,YAAY,OAAO;AACjC,WAAO,IAAI,WAAW,QAAQ,YAAY;AAAA;AAAA;AAK9C,yBAAyB,YAAY;AACnC,SAAO,MAAM,SAAS,MAAM,YAAY,YAAY,MAAM;AAAA;AAG5D,OAAO,UAAU;", "names": []}