{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:44:32:4432"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"warn","message":"Failed to connect to database, but continuing in development mode","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"warn","message":"Make sure to run the init-databases.sql script to set up the database","timestamp":"2025-07-15 16:45:35:4535"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-07-15 16:45:35:4535"}
{"environment":"development","level":"info","message":"Archive Service running on port 3002","port":"3002","timestamp":"2025-07-15 16:45:35:4535","version":"1.0.0"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to database","port":"5432","timestamp":"2025-07-15 16:49:39:4939"}
{"database":"disconnected","level":"info","message":"Health check performed","status":"unhealthy","timestamp":"2025-07-15 16:49:39:4939"}
